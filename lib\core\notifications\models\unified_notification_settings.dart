import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:adhan/adhan.dart' hide Prayer;

import '../../../features/prayer_times/domain/providers/custom_calculation_method_provider.dart';
import 'prayer_notification_settings.dart';
import 'sync_notification_settings.dart';
import 'system_alert_settings.dart';
import 'notification_settings.dart';

/// Unified Notification Settings Data Model
///
/// Comprehensive unified notification settings model following Context7 MCP best practices
/// for consolidating all notification preferences into a single source of truth.
///
/// This model replaces multiple fragmented settings classes with a unified interface
/// that provides type-safe access to all notification configuration options.
///
/// **Key Features:**
/// - Single source of truth for all notification settings
/// - Type-safe access to all configuration options
/// - Comprehensive validation and error handling
/// - Migration support for backward compatibility
/// - Performance-optimized with lazy evaluation
/// - Context7 MCP compliant architecture
/// - Immutable data structure with proper equality and hashing
/// - JSON serialization/deserialization support
/// - Settings versioning for future migrations
/// - Comprehensive validation system
/// - Settings change tracking and auditing
/// - Performance monitoring and analytics
@immutable
class UnifiedNotificationSettingsDataModel {
  /// Settings version for migration support
  final int version;

  /// Last modified timestamp
  final DateTime lastModified;

  /// Settings checksum for integrity validation
  final String? checksum;

  /// User ID for multi-user support
  final String? userId;

  /// Device ID for device-specific settings
  final String? deviceId;

  /// Global notification settings
  final GlobalNotificationSettings global;

  /// Prayer notification settings
  final PrayerNotificationSettings prayer;

  /// Sync notification settings
  final SyncNotificationSettings sync;

  /// System alert settings
  final SystemAlertSettings systemAlert;

  /// General notification settings (from existing model)
  final NotificationSettings general;

  /// Channel-specific settings
  final Map<String, ChannelSettings> channels;

  /// Quiet hours configuration
  final QuietHoursSettings? quietHours;

  /// Permission settings
  final PermissionSettings permissions;

  /// Advanced features
  final AdvancedSettings advanced;

  /// Analytics settings
  final AnalyticsSettings analytics;

  /// Accessibility settings
  final AccessibilitySettings accessibility;

  /// Performance settings
  final PerformanceSettings performance;

  /// Security settings
  final SecuritySettings security;

  /// Creates unified notification settings with the specified configuration
  const UnifiedNotificationSettingsDataModel({
    this.version = 1,
    DateTime? lastModified,
    this.checksum,
    this.userId,
    this.deviceId,
    this.global = const GlobalNotificationSettings(),
    this.prayer = const PrayerNotificationSettings(),
    this.sync = const SyncNotificationSettings(),
    this.systemAlert = const SystemAlertSettings(),
    this.general = const NotificationSettings(),
    this.channels = const {},
    this.quietHours,
    this.permissions = const PermissionSettings(),
    this.advanced = const AdvancedSettings(),
    this.analytics = const AnalyticsSettings(),
    this.accessibility = const AccessibilitySettings(),
    this.performance = const PerformanceSettings(),
    this.security = const SecuritySettings(),
  }) : lastModified = lastModified ?? DateTime.now();

  /// Create default unified notification settings
  factory UnifiedNotificationSettingsDataModel.defaultSettings() {
    return UnifiedNotificationSettingsDataModel(
      version: 1,
      lastModified: DateTime.now(),
      global: GlobalNotificationSettings.defaultSettings(),
      prayer: PrayerNotificationSettings.defaultSettings(),
      sync: SyncNotificationSettings.defaultSettings(),
      systemAlert: SystemAlertSettings.defaultSettings(),
      general: NotificationSettings.defaultSettings(),
      channels: _createDefaultChannelSettings(),
      quietHours: null,
      permissions: PermissionSettings.defaultSettings(),
      advanced: AdvancedSettings.defaultSettings(),
      analytics: AnalyticsSettings.defaultSettings(),
      accessibility: AccessibilitySettings.defaultSettings(),
      performance: PerformanceSettings.defaultSettings(),
      security: SecuritySettings.defaultSettings(),
    );
  }

  /// Create minimal unified notification settings (only essential notifications)
  factory UnifiedNotificationSettingsDataModel.minimal() {
    return UnifiedNotificationSettingsDataModel(
      version: 1,
      lastModified: DateTime.now(),
      global: GlobalNotificationSettings.minimal(),
      prayer: PrayerNotificationSettings.minimal(),
      sync: SyncNotificationSettings.minimal(),
      systemAlert: SystemAlertSettings.minimal(),
      general: NotificationSettings.minimal(),
      channels: _createMinimalChannelSettings(),
      quietHours: const QuietHoursSettings(
        enabled: true,
        startTime: TimeOfDay(hour: 22, minute: 0),
        endTime: TimeOfDay(hour: 7, minute: 0),
      ),
      permissions: PermissionSettings.minimal(),
      advanced: AdvancedSettings.minimal(),
      analytics: AnalyticsSettings.minimal(),
      accessibility: AccessibilitySettings.minimal(),
      performance: PerformanceSettings.minimal(),
      security: SecuritySettings.minimal(),
    );
  }

  /// Create maximum unified notification settings (all features enabled)
  factory UnifiedNotificationSettingsDataModel.maximum() {
    return UnifiedNotificationSettingsDataModel(
      version: 1,
      lastModified: DateTime.now(),
      global: GlobalNotificationSettings.maximum(),
      prayer: PrayerNotificationSettings.maximum(),
      sync: SyncNotificationSettings.detailed(),
      systemAlert: SystemAlertSettings.maximum(),
      general: NotificationSettings.maximum(),
      channels: _createMaximumChannelSettings(),
      quietHours: null, // Disabled for maximum notifications
      permissions: PermissionSettings.maximum(),
      advanced: AdvancedSettings.maximum(),
      analytics: AnalyticsSettings.maximum(),
      accessibility: AccessibilitySettings.maximum(),
      performance: PerformanceSettings.maximum(),
      security: SecuritySettings.maximum(),
    );
  }

  /// Create default channel settings
  static Map<String, ChannelSettings> _createDefaultChannelSettings() {
    return {
      'prayer': const ChannelSettings(
        enabled: true,
        importance: 4,
        sound: true,
        vibration: true,
        lights: false,
        badge: true,
      ),
      'sync': const ChannelSettings(
        enabled: true,
        importance: 2,
        sound: false,
        vibration: false,
        lights: false,
        badge: false,
      ),
      'system_alert': const ChannelSettings(
        enabled: true,
        importance: 5,
        sound: true,
        vibration: true,
        lights: true,
        badge: true,
      ),
      'general': const ChannelSettings(
        enabled: true,
        importance: 3,
        sound: true,
        vibration: true,
        lights: false,
        badge: true,
      ),
    };
  }

  /// Create minimal channel settings
  static Map<String, ChannelSettings> _createMinimalChannelSettings() {
    return {
      'prayer': const ChannelSettings(
        enabled: true,
        importance: 3,
        sound: false,
        vibration: false,
        lights: false,
        badge: false,
      ),
      'sync': const ChannelSettings(enabled: false),
      'system_alert': const ChannelSettings(
        enabled: true,
        importance: 4,
        sound: false,
        vibration: false,
        lights: false,
        badge: false,
      ),
      'general': const ChannelSettings(enabled: false),
    };
  }

  /// Create maximum channel settings
  static Map<String, ChannelSettings> _createMaximumChannelSettings() {
    return {
      'prayer': const ChannelSettings(
        enabled: true,
        importance: 5,
        sound: true,
        vibration: true,
        lights: true,
        badge: true,
        customSound: true,
        customVibration: true,
      ),
      'sync': const ChannelSettings(
        enabled: true,
        importance: 3,
        sound: true,
        vibration: true,
        lights: false,
        badge: true,
      ),
      'system_alert': const ChannelSettings(
        enabled: true,
        importance: 5,
        sound: true,
        vibration: true,
        lights: true,
        badge: true,
        customSound: true,
        customVibration: true,
      ),
      'general': const ChannelSettings(
        enabled: true,
        importance: 4,
        sound: true,
        vibration: true,
        lights: true,
        badge: true,
      ),
    };
  }

  /// Check if notifications are effectively enabled
  bool get isEffectivelyEnabled {
    return global.enabled && permissions.granted;
  }

  /// Check if prayer notifications are enabled
  bool get isPrayerNotificationsEnabled {
    return isEffectivelyEnabled && prayer.globallyEnabled && (channels['prayer']?.enabled ?? false);
  }

  /// Check if sync notifications are enabled
  bool get isSyncNotificationsEnabled {
    return isEffectivelyEnabled && (channels['sync']?.enabled ?? false);
  }

  /// Check if system alerts are enabled
  bool get isSystemAlertsEnabled {
    return isEffectivelyEnabled && systemAlert.enableCriticalAlerts && (channels['system_alert']?.enabled ?? false);
  }

  /// Check if currently in quiet hours
  bool get isInQuietHours {
    return quietHours?.isCurrentlyInQuietHours ?? false;
  }

  /// Get enabled notification channels
  List<String> get enabledChannels {
    return channels.entries.where((entry) => entry.value.enabled).map((entry) => entry.key).toList();
  }

  /// Get disabled notification channels
  List<String> get disabledChannels {
    return channels.entries.where((entry) => !entry.value.enabled).map((entry) => entry.key).toList();
  }

  /// Validate settings configuration
  ValidationResult validate() {
    final errors = <String>[];
    final warnings = <String>[];

    // Check version validity
    if (version < 1) {
      errors.add('Settings version must be at least 1');
    }

    // Check timestamp validity
    final now = DateTime.now();
    if (lastModified.isAfter(now.add(const Duration(minutes: 5)))) {
      warnings.add('Last modified timestamp is in the future');
    }

    // Validate prayer settings
    if (prayer.globallyEnabled) {
      if (prayer.defaultReminderMinutes.isEmpty) {
        errors.add('Prayer reminder minutes cannot be empty when enabled');
      }

      if (prayer.dailySummaryHour < 0 || prayer.dailySummaryHour > 23) {
        errors.add('Daily summary hour must be between 0 and 23');
      }
    }

    // Validate sync settings
    if (sync.progressUpdateThreshold < 1 || sync.progressUpdateThreshold > 100) {
      errors.add('Progress update threshold must be between 1 and 100');
    }

    if (sync.maxActiveNotifications < 1) {
      errors.add('Max active notifications must be at least 1');
    }

    // Validate system alert settings
    if (systemAlert.maxActiveAlerts < 1) {
      errors.add('Max active alerts must be at least 1');
    }

    // Validate quiet hours if enabled
    if (quietHours?.enabled == true && !(quietHours?.isValid() ?? true)) {
      errors.add('Quiet hours configuration is invalid');
    }

    // Validate channels
    for (final entry in channels.entries) {
      final channelName = entry.key;
      final channelSettings = entry.value;

      if (channelSettings.importance < 0 || channelSettings.importance > 5) {
        errors.add('Channel $channelName importance must be between 0 and 5');
      }

      if (channelSettings.customSound && channelSettings.soundPath?.isEmpty == true) {
        errors.add('Channel $channelName has custom sound enabled but no sound path');
      }
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors, warnings: warnings);
  }

  /// Create a copy with updated properties
  UnifiedNotificationSettingsDataModel copyWith({
    int? version,
    DateTime? lastModified,
    String? checksum,
    String? userId,
    String? deviceId,
    GlobalNotificationSettings? global,
    PrayerNotificationSettings? prayer,
    SyncNotificationSettings? sync,
    SystemAlertSettings? systemAlert,
    NotificationSettings? general,
    Map<String, ChannelSettings>? channels,
    QuietHoursSettings? quietHours,
    PermissionSettings? permissions,
    AdvancedSettings? advanced,
    AnalyticsSettings? analytics,
    AccessibilitySettings? accessibility,
    PerformanceSettings? performance,
    SecuritySettings? security,
  }) {
    return UnifiedNotificationSettingsDataModel(
      version: version ?? this.version,
      lastModified: lastModified ?? DateTime.now(),
      checksum: checksum ?? this.checksum,
      userId: userId ?? this.userId,
      deviceId: deviceId ?? this.deviceId,
      global: global ?? this.global,
      prayer: prayer ?? this.prayer,
      sync: sync ?? this.sync,
      systemAlert: systemAlert ?? this.systemAlert,
      general: general ?? this.general,
      channels: channels ?? this.channels,
      quietHours: quietHours ?? this.quietHours,
      permissions: permissions ?? this.permissions,
      advanced: advanced ?? this.advanced,
      analytics: analytics ?? this.analytics,
      accessibility: accessibility ?? this.accessibility,
      performance: performance ?? this.performance,
      security: security ?? this.security,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'lastModified': lastModified.toIso8601String(),
      'checksum': checksum,
      'userId': userId,
      'deviceId': deviceId,
      'global': global.toJson(),
      'prayer': prayer.toJson(),
      'sync': sync.toJson(),
      'systemAlert': systemAlert.toJson(),
      'general': general.toJson(),
      'channels': channels.map((key, value) => MapEntry(key, value.toJson())),
      'quietHours': quietHours?.toJson(),
      'permissions': permissions.toJson(),
      'advanced': advanced.toJson(),
      'analytics': analytics.toJson(),
      'accessibility': accessibility.toJson(),
      'performance': performance.toJson(),
      'security': security.toJson(),
    };
  }

  /// Create from JSON representation
  factory UnifiedNotificationSettingsDataModel.fromJson(Map<String, dynamic> json) {
    return UnifiedNotificationSettingsDataModel(
      version: json['version'] as int? ?? 1,
      lastModified: json['lastModified'] != null ? DateTime.parse(json['lastModified'] as String) : DateTime.now(),
      checksum: json['checksum'] as String?,
      userId: json['userId'] as String?,
      deviceId: json['deviceId'] as String?,
      global: json['global'] != null
          ? GlobalNotificationSettings.fromJson(json['global'] as Map<String, dynamic>)
          : const GlobalNotificationSettings(),
      prayer: json['prayer'] != null
          ? PrayerNotificationSettings.fromJson(json['prayer'] as Map<String, dynamic>)
          : const PrayerNotificationSettings(),
      sync: json['sync'] != null
          ? SyncNotificationSettings.fromJson(json['sync'] as Map<String, dynamic>)
          : const SyncNotificationSettings(),
      systemAlert: json['systemAlert'] != null
          ? SystemAlertSettings.fromJson(json['systemAlert'] as Map<String, dynamic>)
          : const SystemAlertSettings(),
      general: json['general'] != null
          ? NotificationSettings.fromJson(json['general'] as Map<String, dynamic>)
          : const NotificationSettings(),
      channels: _parseChannelSettings(json['channels'] as Map<String, dynamic>?),
      quietHours: json['quietHours'] != null
          ? QuietHoursSettings.fromJson(json['quietHours'] as Map<String, dynamic>)
          : null,
      permissions: json['permissions'] != null
          ? PermissionSettings.fromJson(json['permissions'] as Map<String, dynamic>)
          : const PermissionSettings(),
      advanced: json['advanced'] != null
          ? AdvancedSettings.fromJson(json['advanced'] as Map<String, dynamic>)
          : const AdvancedSettings(),
      analytics: json['analytics'] != null
          ? AnalyticsSettings.fromJson(json['analytics'] as Map<String, dynamic>)
          : const AnalyticsSettings(),
      accessibility: json['accessibility'] != null
          ? AccessibilitySettings.fromJson(json['accessibility'] as Map<String, dynamic>)
          : const AccessibilitySettings(),
      performance: json['performance'] != null
          ? PerformanceSettings.fromJson(json['performance'] as Map<String, dynamic>)
          : const PerformanceSettings(),
      security: json['security'] != null
          ? SecuritySettings.fromJson(json['security'] as Map<String, dynamic>)
          : const SecuritySettings(),
    );
  }

  /// Parse channel settings from JSON
  static Map<String, ChannelSettings> _parseChannelSettings(Map<String, dynamic>? json) {
    if (json == null) return {};

    final result = <String, ChannelSettings>{};

    for (final entry in json.entries) {
      try {
        result[entry.key] = ChannelSettings.fromJson(entry.value as Map<String, dynamic>);
      } catch (e) {
        // Skip invalid channel settings
        debugPrint('Invalid channel settings for ${entry.key}: $e');
      }
    }

    return result;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UnifiedNotificationSettingsDataModel &&
        other.version == version &&
        other.lastModified == lastModified &&
        other.checksum == checksum &&
        other.userId == userId &&
        other.deviceId == deviceId &&
        other.global == global &&
        other.prayer == prayer &&
        other.sync == sync &&
        other.systemAlert == systemAlert &&
        other.general == general &&
        _mapEquals(other.channels, channels) &&
        other.quietHours == quietHours &&
        other.permissions == permissions &&
        other.advanced == advanced &&
        other.analytics == analytics &&
        other.accessibility == accessibility &&
        other.performance == performance &&
        other.security == security;
  }

  @override
  int get hashCode {
    return Object.hash(
      version,
      lastModified,
      checksum,
      userId,
      deviceId,
      global,
      prayer,
      sync,
      systemAlert,
      general,
      channels,
      quietHours,
      permissions,
      advanced,
      analytics,
      accessibility,
      performance,
      security,
    );
  }

  /// Helper method for map equality comparison
  static bool _mapEquals<K, V>(Map<K, V>? a, Map<K, V>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'UnifiedNotificationSettingsDataModel('
        'version: $version, '
        'lastModified: $lastModified, '
        'global: $global, '
        'prayer: $prayer, '
        'sync: $sync, '
        'systemAlert: $systemAlert, '
        'general: $general, '
        'channels: ${channels.length}, '
        'quietHours: $quietHours, '
        'permissions: $permissions, '
        'advanced: $advanced, '
        'analytics: $analytics, '
        'accessibility: $accessibility, '
        'performance: $performance, '
        'security: $security'
        ')';
  }
}

/// Validation Result
///
/// Result of settings validation containing errors and warnings
/// following Context7 MCP best practices for validation reporting.
@immutable
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({required this.isValid, required this.errors, required this.warnings});

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;

  @override
  String toString() {
    return 'ValidationResult(isValid: $isValid, errors: $errors, warnings: $warnings)';
  }
}

/// Global Notification Settings
///
/// Global notification configuration following Context7 MCP best practices
/// for managing application-wide notification behavior.
@immutable
class GlobalNotificationSettings {
  final bool enabled;
  final bool systemNotificationsEnabled;
  final bool backgroundProcessingEnabled;
  final bool respectDoNotDisturb;
  final bool showInForeground;
  final bool groupNotifications;
  final int maxNotifications;
  final bool clearOnDispose;

  const GlobalNotificationSettings({
    this.enabled = true,
    this.systemNotificationsEnabled = true,
    this.backgroundProcessingEnabled = true,
    this.respectDoNotDisturb = true,
    this.showInForeground = true,
    this.groupNotifications = true,
    this.maxNotifications = 50,
    this.clearOnDispose = false,
  });

  factory GlobalNotificationSettings.defaultSettings() {
    return const GlobalNotificationSettings();
  }

  factory GlobalNotificationSettings.minimal() {
    return const GlobalNotificationSettings(
      enabled: true,
      systemNotificationsEnabled: false,
      backgroundProcessingEnabled: false,
      groupNotifications: true,
      maxNotifications: 10,
    );
  }

  factory GlobalNotificationSettings.maximum() {
    return const GlobalNotificationSettings(
      enabled: true,
      systemNotificationsEnabled: true,
      backgroundProcessingEnabled: true,
      respectDoNotDisturb: false,
      showInForeground: true,
      groupNotifications: false,
      maxNotifications: 100,
      clearOnDispose: false,
    );
  }

  GlobalNotificationSettings copyWith({
    bool? enabled,
    bool? systemNotificationsEnabled,
    bool? backgroundProcessingEnabled,
    bool? respectDoNotDisturb,
    bool? showInForeground,
    bool? groupNotifications,
    int? maxNotifications,
    bool? clearOnDispose,
  }) {
    return GlobalNotificationSettings(
      enabled: enabled ?? this.enabled,
      systemNotificationsEnabled: systemNotificationsEnabled ?? this.systemNotificationsEnabled,
      backgroundProcessingEnabled: backgroundProcessingEnabled ?? this.backgroundProcessingEnabled,
      respectDoNotDisturb: respectDoNotDisturb ?? this.respectDoNotDisturb,
      showInForeground: showInForeground ?? this.showInForeground,
      groupNotifications: groupNotifications ?? this.groupNotifications,
      maxNotifications: maxNotifications ?? this.maxNotifications,
      clearOnDispose: clearOnDispose ?? this.clearOnDispose,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'systemNotificationsEnabled': systemNotificationsEnabled,
      'backgroundProcessingEnabled': backgroundProcessingEnabled,
      'respectDoNotDisturb': respectDoNotDisturb,
      'showInForeground': showInForeground,
      'groupNotifications': groupNotifications,
      'maxNotifications': maxNotifications,
      'clearOnDispose': clearOnDispose,
    };
  }

  factory GlobalNotificationSettings.fromJson(Map<String, dynamic> json) {
    return GlobalNotificationSettings(
      enabled: json['enabled'] as bool? ?? true,
      systemNotificationsEnabled: json['systemNotificationsEnabled'] as bool? ?? true,
      backgroundProcessingEnabled: json['backgroundProcessingEnabled'] as bool? ?? true,
      respectDoNotDisturb: json['respectDoNotDisturb'] as bool? ?? true,
      showInForeground: json['showInForeground'] as bool? ?? true,
      groupNotifications: json['groupNotifications'] as bool? ?? true,
      maxNotifications: json['maxNotifications'] as int? ?? 50,
      clearOnDispose: json['clearOnDispose'] as bool? ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GlobalNotificationSettings &&
        other.enabled == enabled &&
        other.systemNotificationsEnabled == systemNotificationsEnabled &&
        other.backgroundProcessingEnabled == backgroundProcessingEnabled &&
        other.respectDoNotDisturb == respectDoNotDisturb &&
        other.showInForeground == showInForeground &&
        other.groupNotifications == groupNotifications &&
        other.maxNotifications == maxNotifications &&
        other.clearOnDispose == clearOnDispose;
  }

  @override
  int get hashCode {
    return Object.hash(
      enabled,
      systemNotificationsEnabled,
      backgroundProcessingEnabled,
      respectDoNotDisturb,
      showInForeground,
      groupNotifications,
      maxNotifications,
      clearOnDispose,
    );
  }

  @override
  String toString() {
    return 'GlobalNotificationSettings('
        'enabled: $enabled, '
        'systemNotificationsEnabled: $systemNotificationsEnabled, '
        'backgroundProcessingEnabled: $backgroundProcessingEnabled'
        ')';
  }
}

/// Channel Settings
///
/// Individual notification channel configuration following Context7 MCP best practices
/// for managing channel-specific notification behavior.
@immutable
class ChannelSettings {
  final bool enabled;
  final int importance;
  final bool sound;
  final bool vibration;
  final bool lights;
  final bool badge;
  final bool customSound;
  final bool customVibration;
  final String? soundPath;
  final List<int>? vibrationPattern;
  final int? ledColor;

  const ChannelSettings({
    this.enabled = true,
    this.importance = 3,
    this.sound = true,
    this.vibration = true,
    this.lights = false,
    this.badge = true,
    this.customSound = false,
    this.customVibration = false,
    this.soundPath,
    this.vibrationPattern,
    this.ledColor,
  });

  ChannelSettings copyWith({
    bool? enabled,
    int? importance,
    bool? sound,
    bool? vibration,
    bool? lights,
    bool? badge,
    bool? customSound,
    bool? customVibration,
    String? soundPath,
    List<int>? vibrationPattern,
    int? ledColor,
  }) {
    return ChannelSettings(
      enabled: enabled ?? this.enabled,
      importance: importance ?? this.importance,
      sound: sound ?? this.sound,
      vibration: vibration ?? this.vibration,
      lights: lights ?? this.lights,
      badge: badge ?? this.badge,
      customSound: customSound ?? this.customSound,
      customVibration: customVibration ?? this.customVibration,
      soundPath: soundPath ?? this.soundPath,
      vibrationPattern: vibrationPattern ?? this.vibrationPattern,
      ledColor: ledColor ?? this.ledColor,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'importance': importance,
      'sound': sound,
      'vibration': vibration,
      'lights': lights,
      'badge': badge,
      'customSound': customSound,
      'customVibration': customVibration,
      'soundPath': soundPath,
      'vibrationPattern': vibrationPattern,
      'ledColor': ledColor,
    };
  }

  factory ChannelSettings.fromJson(Map<String, dynamic> json) {
    return ChannelSettings(
      enabled: json['enabled'] as bool? ?? true,
      importance: json['importance'] as int? ?? 3,
      sound: json['sound'] as bool? ?? true,
      vibration: json['vibration'] as bool? ?? true,
      lights: json['lights'] as bool? ?? false,
      badge: json['badge'] as bool? ?? true,
      customSound: json['customSound'] as bool? ?? false,
      customVibration: json['customVibration'] as bool? ?? false,
      soundPath: json['soundPath'] as String?,
      vibrationPattern: (json['vibrationPattern'] as List<dynamic>?)?.cast<int>(),
      ledColor: json['ledColor'] as int?,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChannelSettings &&
        other.enabled == enabled &&
        other.importance == importance &&
        other.sound == sound &&
        other.vibration == vibration &&
        other.lights == lights &&
        other.badge == badge &&
        other.customSound == customSound &&
        other.customVibration == customVibration &&
        other.soundPath == soundPath &&
        _listEquals(other.vibrationPattern, vibrationPattern) &&
        other.ledColor == ledColor;
  }

  @override
  int get hashCode {
    return Object.hash(
      enabled,
      importance,
      sound,
      vibration,
      lights,
      badge,
      customSound,
      customVibration,
      soundPath,
      vibrationPattern,
      ledColor,
    );
  }

  /// Helper method for list equality comparison
  static bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (var i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'ChannelSettings('
        'enabled: $enabled, '
        'importance: $importance, '
        'sound: $sound, '
        'vibration: $vibration'
        ')';
  }
}

/// Quiet Hours Settings
///
/// Quiet hours configuration following Context7 MCP best practices
/// for managing notification quiet periods.
@immutable
class QuietHoursSettings {
  final bool enabled;
  final TimeOfDay startTime;
  final TimeOfDay endTime;
  final List<int> daysOfWeek;
  final List<String> exceptions;

  const QuietHoursSettings({
    this.enabled = false,
    this.startTime = const TimeOfDay(hour: 22, minute: 0),
    this.endTime = const TimeOfDay(hour: 7, minute: 0),
    this.daysOfWeek = const [1, 2, 3, 4, 5, 6, 7], // All days
    this.exceptions = const [],
  });

  bool get isCurrentlyInQuietHours {
    if (!enabled) return false;

    final now = TimeOfDay.now();
    final currentDay = DateTime.now().weekday;

    if (!daysOfWeek.contains(currentDay)) return false;

    // Handle overnight quiet hours (e.g., 22:00 to 07:00)
    if (startTime.hour > endTime.hour) {
      return now.hour >= startTime.hour || now.hour < endTime.hour;
    } else {
      return now.hour >= startTime.hour && now.hour < endTime.hour;
    }
  }

  bool isValid() {
    return daysOfWeek.isNotEmpty && daysOfWeek.every((day) => day >= 1 && day <= 7);
  }

  QuietHoursSettings copyWith({
    bool? enabled,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
    List<int>? daysOfWeek,
    List<String>? exceptions,
  }) {
    return QuietHoursSettings(
      enabled: enabled ?? this.enabled,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      exceptions: exceptions ?? this.exceptions,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'startTime': {'hour': startTime.hour, 'minute': startTime.minute},
      'endTime': {'hour': endTime.hour, 'minute': endTime.minute},
      'daysOfWeek': daysOfWeek,
      'exceptions': exceptions,
    };
  }

  factory QuietHoursSettings.fromJson(Map<String, dynamic> json) {
    final startTimeMap = json['startTime'] as Map<String, dynamic>? ?? {'hour': 22, 'minute': 0};
    final endTimeMap = json['endTime'] as Map<String, dynamic>? ?? {'hour': 7, 'minute': 0};

    return QuietHoursSettings(
      enabled: json['enabled'] as bool? ?? false,
      startTime: TimeOfDay(hour: startTimeMap['hour'] as int? ?? 22, minute: startTimeMap['minute'] as int? ?? 0),
      endTime: TimeOfDay(hour: endTimeMap['hour'] as int? ?? 7, minute: endTimeMap['minute'] as int? ?? 0),
      daysOfWeek: (json['daysOfWeek'] as List<dynamic>?)?.cast<int>() ?? [1, 2, 3, 4, 5, 6, 7],
      exceptions: (json['exceptions'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuietHoursSettings &&
        other.enabled == enabled &&
        other.startTime == startTime &&
        other.endTime == endTime &&
        _listEquals(other.daysOfWeek, daysOfWeek) &&
        _listEquals(other.exceptions, exceptions);
  }

  @override
  int get hashCode {
    return Object.hash(enabled, startTime, endTime, daysOfWeek, exceptions);
  }

  /// Helper method for list equality comparison
  static bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (var i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  @override
  String toString() {
    return 'QuietHoursSettings('
        'enabled: $enabled, '
        'startTime: $startTime, '
        'endTime: $endTime, '
        'daysOfWeek: $daysOfWeek'
        ')';
  }
}

/// Permission Settings
///
/// Notification permission configuration following Context7 MCP best practices
/// for managing notification permissions and user consent.
@immutable
class PermissionSettings {
  final bool granted;
  final bool exactAlarmGranted;
  final bool requestOnStartup;
  final bool showRationale;
  final bool gracefulDegradation;

  const PermissionSettings({
    this.granted = false,
    this.exactAlarmGranted = false,
    this.requestOnStartup = true,
    this.showRationale = true,
    this.gracefulDegradation = true,
  });

  factory PermissionSettings.defaultSettings() {
    return const PermissionSettings();
  }

  factory PermissionSettings.minimal() {
    return const PermissionSettings(requestOnStartup: false, showRationale: false);
  }

  factory PermissionSettings.maximum() {
    return const PermissionSettings(
      granted: true,
      exactAlarmGranted: true,
      requestOnStartup: true,
      showRationale: true,
      gracefulDegradation: true,
    );
  }

  PermissionSettings copyWith({
    bool? granted,
    bool? exactAlarmGranted,
    bool? requestOnStartup,
    bool? showRationale,
    bool? gracefulDegradation,
  }) {
    return PermissionSettings(
      granted: granted ?? this.granted,
      exactAlarmGranted: exactAlarmGranted ?? this.exactAlarmGranted,
      requestOnStartup: requestOnStartup ?? this.requestOnStartup,
      showRationale: showRationale ?? this.showRationale,
      gracefulDegradation: gracefulDegradation ?? this.gracefulDegradation,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'granted': granted,
      'exactAlarmGranted': exactAlarmGranted,
      'requestOnStartup': requestOnStartup,
      'showRationale': showRationale,
      'gracefulDegradation': gracefulDegradation,
    };
  }

  factory PermissionSettings.fromJson(Map<String, dynamic> json) {
    return PermissionSettings(
      granted: json['granted'] as bool? ?? false,
      exactAlarmGranted: json['exactAlarmGranted'] as bool? ?? false,
      requestOnStartup: json['requestOnStartup'] as bool? ?? true,
      showRationale: json['showRationale'] as bool? ?? true,
      gracefulDegradation: json['gracefulDegradation'] as bool? ?? true,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PermissionSettings &&
        other.granted == granted &&
        other.exactAlarmGranted == exactAlarmGranted &&
        other.requestOnStartup == requestOnStartup &&
        other.showRationale == showRationale &&
        other.gracefulDegradation == gracefulDegradation;
  }

  @override
  int get hashCode {
    return Object.hash(granted, exactAlarmGranted, requestOnStartup, showRationale, gracefulDegradation);
  }

  @override
  String toString() {
    return 'PermissionSettings('
        'granted: $granted, '
        'exactAlarmGranted: $exactAlarmGranted, '
        'requestOnStartup: $requestOnStartup'
        ')';
  }
}

/// Advanced Settings
///
/// Advanced notification features following Context7 MCP best practices
/// for managing sophisticated notification behaviors.
@immutable
class AdvancedSettings {
  final bool enableBatching;
  final bool enableGrouping;
  final bool enableSmartDelivery;
  final bool enablePredictiveDelivery;
  final bool enableAdaptiveTiming;
  final int maxBatchSize;
  final Duration batchDelay;
  final bool enableRateLimiting;
  final int maxNotificationsPerMinute;

  const AdvancedSettings({
    this.enableBatching = false,
    this.enableGrouping = true,
    this.enableSmartDelivery = false,
    this.enablePredictiveDelivery = false,
    this.enableAdaptiveTiming = false,
    this.maxBatchSize = 5,
    this.batchDelay = const Duration(minutes: 2),
    this.enableRateLimiting = true,
    this.maxNotificationsPerMinute = 10,
  });

  factory AdvancedSettings.defaultSettings() {
    return const AdvancedSettings();
  }

  factory AdvancedSettings.minimal() {
    return const AdvancedSettings(
      enableBatching: true,
      enableGrouping: true,
      enableSmartDelivery: false,
      enablePredictiveDelivery: false,
      enableAdaptiveTiming: false,
      maxBatchSize: 3,
      enableRateLimiting: true,
      maxNotificationsPerMinute: 5,
    );
  }

  factory AdvancedSettings.maximum() {
    return const AdvancedSettings(
      enableBatching: false,
      enableGrouping: false,
      enableSmartDelivery: true,
      enablePredictiveDelivery: true,
      enableAdaptiveTiming: true,
      maxBatchSize: 10,
      batchDelay: Duration(seconds: 30),
      enableRateLimiting: false,
      maxNotificationsPerMinute: 60,
    );
  }

  AdvancedSettings copyWith({
    bool? enableBatching,
    bool? enableGrouping,
    bool? enableSmartDelivery,
    bool? enablePredictiveDelivery,
    bool? enableAdaptiveTiming,
    int? maxBatchSize,
    Duration? batchDelay,
    bool? enableRateLimiting,
    int? maxNotificationsPerMinute,
  }) {
    return AdvancedSettings(
      enableBatching: enableBatching ?? this.enableBatching,
      enableGrouping: enableGrouping ?? this.enableGrouping,
      enableSmartDelivery: enableSmartDelivery ?? this.enableSmartDelivery,
      enablePredictiveDelivery: enablePredictiveDelivery ?? this.enablePredictiveDelivery,
      enableAdaptiveTiming: enableAdaptiveTiming ?? this.enableAdaptiveTiming,
      maxBatchSize: maxBatchSize ?? this.maxBatchSize,
      batchDelay: batchDelay ?? this.batchDelay,
      enableRateLimiting: enableRateLimiting ?? this.enableRateLimiting,
      maxNotificationsPerMinute: maxNotificationsPerMinute ?? this.maxNotificationsPerMinute,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableBatching': enableBatching,
      'enableGrouping': enableGrouping,
      'enableSmartDelivery': enableSmartDelivery,
      'enablePredictiveDelivery': enablePredictiveDelivery,
      'enableAdaptiveTiming': enableAdaptiveTiming,
      'maxBatchSize': maxBatchSize,
      'batchDelay': batchDelay.inMilliseconds,
      'enableRateLimiting': enableRateLimiting,
      'maxNotificationsPerMinute': maxNotificationsPerMinute,
    };
  }

  factory AdvancedSettings.fromJson(Map<String, dynamic> json) {
    return AdvancedSettings(
      enableBatching: json['enableBatching'] as bool? ?? false,
      enableGrouping: json['enableGrouping'] as bool? ?? true,
      enableSmartDelivery: json['enableSmartDelivery'] as bool? ?? false,
      enablePredictiveDelivery: json['enablePredictiveDelivery'] as bool? ?? false,
      enableAdaptiveTiming: json['enableAdaptiveTiming'] as bool? ?? false,
      maxBatchSize: json['maxBatchSize'] as int? ?? 5,
      batchDelay: Duration(milliseconds: json['batchDelay'] as int? ?? 120000),
      enableRateLimiting: json['enableRateLimiting'] as bool? ?? true,
      maxNotificationsPerMinute: json['maxNotificationsPerMinute'] as int? ?? 10,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdvancedSettings &&
        other.enableBatching == enableBatching &&
        other.enableGrouping == enableGrouping &&
        other.enableSmartDelivery == enableSmartDelivery &&
        other.enablePredictiveDelivery == enablePredictiveDelivery &&
        other.enableAdaptiveTiming == enableAdaptiveTiming &&
        other.maxBatchSize == maxBatchSize &&
        other.batchDelay == batchDelay &&
        other.enableRateLimiting == enableRateLimiting &&
        other.maxNotificationsPerMinute == maxNotificationsPerMinute;
  }

  @override
  int get hashCode {
    return Object.hash(
      enableBatching,
      enableGrouping,
      enableSmartDelivery,
      enablePredictiveDelivery,
      enableAdaptiveTiming,
      maxBatchSize,
      batchDelay,
      enableRateLimiting,
      maxNotificationsPerMinute,
    );
  }

  @override
  String toString() {
    return 'AdvancedSettings('
        'enableBatching: $enableBatching, '
        'enableGrouping: $enableGrouping, '
        'enableSmartDelivery: $enableSmartDelivery'
        ')';
  }
}

/// Analytics Settings
///
/// Notification analytics configuration following Context7 MCP best practices
/// for managing notification tracking and performance monitoring.
@immutable
class AnalyticsSettings {
  final bool enableTracking;
  final bool enablePerformanceMetrics;
  final bool enableDetailedLogging;
  final bool enableUserBehaviorTracking;
  final Duration retentionPeriod;

  const AnalyticsSettings({
    this.enableTracking = false,
    this.enablePerformanceMetrics = false,
    this.enableDetailedLogging = false,
    this.enableUserBehaviorTracking = false,
    this.retentionPeriod = const Duration(days: 30),
  });

  factory AnalyticsSettings.defaultSettings() {
    return const AnalyticsSettings();
  }

  factory AnalyticsSettings.minimal() {
    return const AnalyticsSettings(
      enableTracking: false,
      enablePerformanceMetrics: false,
      enableDetailedLogging: false,
      enableUserBehaviorTracking: false,
      retentionPeriod: Duration(days: 7),
    );
  }

  factory AnalyticsSettings.maximum() {
    return const AnalyticsSettings(
      enableTracking: true,
      enablePerformanceMetrics: true,
      enableDetailedLogging: true,
      enableUserBehaviorTracking: true,
      retentionPeriod: Duration(days: 90),
    );
  }

  AnalyticsSettings copyWith({
    bool? enableTracking,
    bool? enablePerformanceMetrics,
    bool? enableDetailedLogging,
    bool? enableUserBehaviorTracking,
    Duration? retentionPeriod,
  }) {
    return AnalyticsSettings(
      enableTracking: enableTracking ?? this.enableTracking,
      enablePerformanceMetrics: enablePerformanceMetrics ?? this.enablePerformanceMetrics,
      enableDetailedLogging: enableDetailedLogging ?? this.enableDetailedLogging,
      enableUserBehaviorTracking: enableUserBehaviorTracking ?? this.enableUserBehaviorTracking,
      retentionPeriod: retentionPeriod ?? this.retentionPeriod,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableTracking': enableTracking,
      'enablePerformanceMetrics': enablePerformanceMetrics,
      'enableDetailedLogging': enableDetailedLogging,
      'enableUserBehaviorTracking': enableUserBehaviorTracking,
      'retentionPeriod': retentionPeriod.inDays,
    };
  }

  factory AnalyticsSettings.fromJson(Map<String, dynamic> json) {
    return AnalyticsSettings(
      enableTracking: json['enableTracking'] as bool? ?? false,
      enablePerformanceMetrics: json['enablePerformanceMetrics'] as bool? ?? false,
      enableDetailedLogging: json['enableDetailedLogging'] as bool? ?? false,
      enableUserBehaviorTracking: json['enableUserBehaviorTracking'] as bool? ?? false,
      retentionPeriod: Duration(days: json['retentionPeriod'] as int? ?? 30),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AnalyticsSettings &&
        other.enableTracking == enableTracking &&
        other.enablePerformanceMetrics == enablePerformanceMetrics &&
        other.enableDetailedLogging == enableDetailedLogging &&
        other.enableUserBehaviorTracking == enableUserBehaviorTracking &&
        other.retentionPeriod == retentionPeriod;
  }

  @override
  int get hashCode {
    return Object.hash(
      enableTracking,
      enablePerformanceMetrics,
      enableDetailedLogging,
      enableUserBehaviorTracking,
      retentionPeriod,
    );
  }

  @override
  String toString() {
    return 'AnalyticsSettings('
        'enableTracking: $enableTracking, '
        'enablePerformanceMetrics: $enablePerformanceMetrics, '
        'enableDetailedLogging: $enableDetailedLogging'
        ')';
  }
}

/// Accessibility Settings
///
/// Notification accessibility configuration following Context7 MCP best practices
/// for managing accessibility features and compliance.
@immutable
class AccessibilitySettings {
  final bool enableHighContrast;
  final bool enableLargeText;
  final bool enableScreenReader;
  final bool enableVoiceAnnouncements;
  final bool enableHapticFeedback;
  final double textScaleFactor;

  const AccessibilitySettings({
    this.enableHighContrast = false,
    this.enableLargeText = false,
    this.enableScreenReader = false,
    this.enableVoiceAnnouncements = false,
    this.enableHapticFeedback = true,
    this.textScaleFactor = 1.0,
  });

  factory AccessibilitySettings.defaultSettings() {
    return const AccessibilitySettings();
  }

  factory AccessibilitySettings.minimal() {
    return const AccessibilitySettings(
      enableHighContrast: false,
      enableLargeText: false,
      enableScreenReader: false,
      enableVoiceAnnouncements: false,
      enableHapticFeedback: false,
      textScaleFactor: 1.0,
    );
  }

  factory AccessibilitySettings.maximum() {
    return const AccessibilitySettings(
      enableHighContrast: true,
      enableLargeText: true,
      enableScreenReader: true,
      enableVoiceAnnouncements: true,
      enableHapticFeedback: true,
      textScaleFactor: 1.5,
    );
  }

  AccessibilitySettings copyWith({
    bool? enableHighContrast,
    bool? enableLargeText,
    bool? enableScreenReader,
    bool? enableVoiceAnnouncements,
    bool? enableHapticFeedback,
    double? textScaleFactor,
  }) {
    return AccessibilitySettings(
      enableHighContrast: enableHighContrast ?? this.enableHighContrast,
      enableLargeText: enableLargeText ?? this.enableLargeText,
      enableScreenReader: enableScreenReader ?? this.enableScreenReader,
      enableVoiceAnnouncements: enableVoiceAnnouncements ?? this.enableVoiceAnnouncements,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      textScaleFactor: textScaleFactor ?? this.textScaleFactor,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableHighContrast': enableHighContrast,
      'enableLargeText': enableLargeText,
      'enableScreenReader': enableScreenReader,
      'enableVoiceAnnouncements': enableVoiceAnnouncements,
      'enableHapticFeedback': enableHapticFeedback,
      'textScaleFactor': textScaleFactor,
    };
  }

  factory AccessibilitySettings.fromJson(Map<String, dynamic> json) {
    return AccessibilitySettings(
      enableHighContrast: json['enableHighContrast'] as bool? ?? false,
      enableLargeText: json['enableLargeText'] as bool? ?? false,
      enableScreenReader: json['enableScreenReader'] as bool? ?? false,
      enableVoiceAnnouncements: json['enableVoiceAnnouncements'] as bool? ?? false,
      enableHapticFeedback: json['enableHapticFeedback'] as bool? ?? true,
      textScaleFactor: (json['textScaleFactor'] as num?)?.toDouble() ?? 1.0,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AccessibilitySettings &&
        other.enableHighContrast == enableHighContrast &&
        other.enableLargeText == enableLargeText &&
        other.enableScreenReader == enableScreenReader &&
        other.enableVoiceAnnouncements == enableVoiceAnnouncements &&
        other.enableHapticFeedback == enableHapticFeedback &&
        other.textScaleFactor == textScaleFactor;
  }

  @override
  int get hashCode {
    return Object.hash(
      enableHighContrast,
      enableLargeText,
      enableScreenReader,
      enableVoiceAnnouncements,
      enableHapticFeedback,
      textScaleFactor,
    );
  }

  @override
  String toString() {
    return 'AccessibilitySettings('
        'enableHighContrast: $enableHighContrast, '
        'enableLargeText: $enableLargeText, '
        'enableScreenReader: $enableScreenReader'
        ')';
  }
}

/// Performance Settings
///
/// Notification performance configuration following Context7 MCP best practices
/// for managing notification system performance and resource usage.
@immutable
class PerformanceSettings {
  final bool enableOptimizations;
  final bool enableCaching;
  final bool enableLazyLoading;
  final bool enableMemoryOptimization;
  final int maxCacheSize;
  final Duration cacheExpiration;
  final int maxConcurrentNotifications;

  const PerformanceSettings({
    this.enableOptimizations = true,
    this.enableCaching = true,
    this.enableLazyLoading = true,
    this.enableMemoryOptimization = true,
    this.maxCacheSize = 100,
    this.cacheExpiration = const Duration(hours: 24),
    this.maxConcurrentNotifications = 10,
  });

  factory PerformanceSettings.defaultSettings() {
    return const PerformanceSettings();
  }

  factory PerformanceSettings.minimal() {
    return const PerformanceSettings(
      enableOptimizations: true,
      enableCaching: false,
      enableLazyLoading: true,
      enableMemoryOptimization: true,
      maxCacheSize: 50,
      cacheExpiration: Duration(hours: 12),
      maxConcurrentNotifications: 5,
    );
  }

  factory PerformanceSettings.maximum() {
    return const PerformanceSettings(
      enableOptimizations: true,
      enableCaching: true,
      enableLazyLoading: true,
      enableMemoryOptimization: true,
      maxCacheSize: 500,
      cacheExpiration: Duration(days: 7),
      maxConcurrentNotifications: 50,
    );
  }

  PerformanceSettings copyWith({
    bool? enableOptimizations,
    bool? enableCaching,
    bool? enableLazyLoading,
    bool? enableMemoryOptimization,
    int? maxCacheSize,
    Duration? cacheExpiration,
    int? maxConcurrentNotifications,
  }) {
    return PerformanceSettings(
      enableOptimizations: enableOptimizations ?? this.enableOptimizations,
      enableCaching: enableCaching ?? this.enableCaching,
      enableLazyLoading: enableLazyLoading ?? this.enableLazyLoading,
      enableMemoryOptimization: enableMemoryOptimization ?? this.enableMemoryOptimization,
      maxCacheSize: maxCacheSize ?? this.maxCacheSize,
      cacheExpiration: cacheExpiration ?? this.cacheExpiration,
      maxConcurrentNotifications: maxConcurrentNotifications ?? this.maxConcurrentNotifications,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableOptimizations': enableOptimizations,
      'enableCaching': enableCaching,
      'enableLazyLoading': enableLazyLoading,
      'enableMemoryOptimization': enableMemoryOptimization,
      'maxCacheSize': maxCacheSize,
      'cacheExpiration': cacheExpiration.inMilliseconds,
      'maxConcurrentNotifications': maxConcurrentNotifications,
    };
  }

  factory PerformanceSettings.fromJson(Map<String, dynamic> json) {
    return PerformanceSettings(
      enableOptimizations: json['enableOptimizations'] as bool? ?? true,
      enableCaching: json['enableCaching'] as bool? ?? true,
      enableLazyLoading: json['enableLazyLoading'] as bool? ?? true,
      enableMemoryOptimization: json['enableMemoryOptimization'] as bool? ?? true,
      maxCacheSize: json['maxCacheSize'] as int? ?? 100,
      cacheExpiration: Duration(milliseconds: json['cacheExpiration'] as int? ?? 86400000),
      maxConcurrentNotifications: json['maxConcurrentNotifications'] as int? ?? 10,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PerformanceSettings &&
        other.enableOptimizations == enableOptimizations &&
        other.enableCaching == enableCaching &&
        other.enableLazyLoading == enableLazyLoading &&
        other.enableMemoryOptimization == enableMemoryOptimization &&
        other.maxCacheSize == maxCacheSize &&
        other.cacheExpiration == cacheExpiration &&
        other.maxConcurrentNotifications == maxConcurrentNotifications;
  }

  @override
  int get hashCode {
    return Object.hash(
      enableOptimizations,
      enableCaching,
      enableLazyLoading,
      enableMemoryOptimization,
      maxCacheSize,
      cacheExpiration,
      maxConcurrentNotifications,
    );
  }

  @override
  String toString() {
    return 'PerformanceSettings('
        'enableOptimizations: $enableOptimizations, '
        'enableCaching: $enableCaching, '
        'enableLazyLoading: $enableLazyLoading'
        ')';
  }
}

/// Security Settings
///
/// Notification security configuration following Context7 MCP best practices
/// for managing notification security and privacy features.
@immutable
class SecuritySettings {
  final bool enableEncryption;
  final bool enableDataValidation;
  final bool enableSecureStorage;
  final bool enablePrivacyMode;
  final bool enableAuditLogging;
  final bool enableIntegrityChecks;
  final Duration sessionTimeout;

  const SecuritySettings({
    this.enableEncryption = true,
    this.enableDataValidation = true,
    this.enableSecureStorage = true,
    this.enablePrivacyMode = false,
    this.enableAuditLogging = false,
    this.enableIntegrityChecks = true,
    this.sessionTimeout = const Duration(hours: 24),
  });

  factory SecuritySettings.defaultSettings() {
    return const SecuritySettings();
  }

  factory SecuritySettings.minimal() {
    return const SecuritySettings(
      enableEncryption: false,
      enableDataValidation: true,
      enableSecureStorage: false,
      enablePrivacyMode: false,
      enableAuditLogging: false,
      enableIntegrityChecks: false,
      sessionTimeout: Duration(hours: 12),
    );
  }

  factory SecuritySettings.maximum() {
    return const SecuritySettings(
      enableEncryption: true,
      enableDataValidation: true,
      enableSecureStorage: true,
      enablePrivacyMode: true,
      enableAuditLogging: true,
      enableIntegrityChecks: true,
      sessionTimeout: Duration(hours: 1),
    );
  }

  SecuritySettings copyWith({
    bool? enableEncryption,
    bool? enableDataValidation,
    bool? enableSecureStorage,
    bool? enablePrivacyMode,
    bool? enableAuditLogging,
    bool? enableIntegrityChecks,
    Duration? sessionTimeout,
  }) {
    return SecuritySettings(
      enableEncryption: enableEncryption ?? this.enableEncryption,
      enableDataValidation: enableDataValidation ?? this.enableDataValidation,
      enableSecureStorage: enableSecureStorage ?? this.enableSecureStorage,
      enablePrivacyMode: enablePrivacyMode ?? this.enablePrivacyMode,
      enableAuditLogging: enableAuditLogging ?? this.enableAuditLogging,
      enableIntegrityChecks: enableIntegrityChecks ?? this.enableIntegrityChecks,
      sessionTimeout: sessionTimeout ?? this.sessionTimeout,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableEncryption': enableEncryption,
      'enableDataValidation': enableDataValidation,
      'enableSecureStorage': enableSecureStorage,
      'enablePrivacyMode': enablePrivacyMode,
      'enableAuditLogging': enableAuditLogging,
      'enableIntegrityChecks': enableIntegrityChecks,
      'sessionTimeout': sessionTimeout.inMilliseconds,
    };
  }

  factory SecuritySettings.fromJson(Map<String, dynamic> json) {
    return SecuritySettings(
      enableEncryption: json['enableEncryption'] as bool? ?? true,
      enableDataValidation: json['enableDataValidation'] as bool? ?? true,
      enableSecureStorage: json['enableSecureStorage'] as bool? ?? true,
      enablePrivacyMode: json['enablePrivacyMode'] as bool? ?? false,
      enableAuditLogging: json['enableAuditLogging'] as bool? ?? false,
      enableIntegrityChecks: json['enableIntegrityChecks'] as bool? ?? true,
      sessionTimeout: Duration(milliseconds: json['sessionTimeout'] as int? ?? 86400000),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SecuritySettings &&
        other.enableEncryption == enableEncryption &&
        other.enableDataValidation == enableDataValidation &&
        other.enableSecureStorage == enableSecureStorage &&
        other.enablePrivacyMode == enablePrivacyMode &&
        other.enableAuditLogging == enableAuditLogging &&
        other.enableIntegrityChecks == enableIntegrityChecks &&
        other.sessionTimeout == sessionTimeout;
  }

  @override
  int get hashCode {
    return Object.hash(
      enableEncryption,
      enableDataValidation,
      enableSecureStorage,
      enablePrivacyMode,
      enableAuditLogging,
      enableIntegrityChecks,
      sessionTimeout,
    );
  }

  @override
  String toString() {
    return 'SecuritySettings('
        'enableEncryption: $enableEncryption, '
        'enableDataValidation: $enableDataValidation, '
        'enableSecureStorage: $enableSecureStorage'
        ')';
  }
}
