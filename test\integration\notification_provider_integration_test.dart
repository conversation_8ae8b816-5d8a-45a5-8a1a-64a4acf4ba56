import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:riverpod/riverpod.dart';

import '../../lib/core/notifications/providers/unified_notification_provider.dart';

/// Integration tests for UnifiedNotificationProvider following Context7 MCP best practices
///
/// **Test Coverage:**
/// - Unified notification provider lifecycle and state management
/// - Real-world usage scenarios with unified notification system
/// - Error handling and recovery for unified providers
/// - Performance under load for consolidated notification system
/// - State persistence and restoration for unified settings
///
/// **Migration Note:** This test suite has been updated to test the unified notification
/// provider system instead of the legacy notification settings provider.
void main() {
  group('UnifiedNotificationProvider Integration Tests', () {
    late ProviderContainer container;

    /// Setup for each test following Context7 MCP best practices
    setUpAll(() {
      // Initialize Flutter test binding
      TestWidgetsFlutterBinding.ensureInitialized();

      // Mock platform channels for path_provider
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        const MethodChannel('plugins.flutter.io/path_provider'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'getApplicationDocumentsDirectory') {
            return '/tmp/test_documents';
          }
          return null;
        },
      );
    });

    setUp(() {
      container = ProviderContainer();
    });

    /// Cleanup after each test
    tearDown(() {
      container.dispose();
    });

    group('Provider Lifecycle Integration', () {
      test('should initialize provider with default state', () async {
        // Act: Read the provider for the first time
        final state = await container.read(notificationSettingsProvider.future);

        // Assert: Should have default state
        expect(state, isA<NotificationSettingsState>());
        expect(state.notificationsEnabled, isTrue);
      });

      test('should handle provider invalidation and recreation', () async {
        // Arrange: Get initial state
        final initialState = await container.read(notificationSettingsProvider.future);
        expect(initialState.notificationsEnabled, isTrue);

        // Act: Invalidate the provider
        container.invalidate(notificationSettingsProvider);
        final newState = await container.read(notificationSettingsProvider.future);

        // Assert: Should recreate with fresh state
        expect(newState.notificationsEnabled, isTrue);
      });

      test('should maintain state consistency across multiple reads', () async {
        // Act: Read provider multiple times
        final state1 = container.read(notificationSettingsProvider);
        final state2 = container.read(notificationSettingsProvider);
        final state3 = container.read(notificationSettingsProvider);

        // Assert: Should return same instance
        expect(identical(state1, state2), isTrue);
        expect(identical(state2, state3), isTrue);
      });
    });

    group('Provider State Management', () {
      test('should handle unified notification settings updates', () async {
        // Arrange: Get initial state
        final notifier = container.read(unifiedNotificationSettingsNotifierProvider.notifier);

        // Act: Update global settings
        await notifier.updateGlobalSettings(globallyEnabled: true);

        // Assert: Should update state
        final state = await container.read(unifiedNotificationSettingsNotifierProvider.future);
        expect(state, isNotNull);
        expect(state.globallyEnabled, isTrue);
      });

      test('should handle prayer settings updates', () async {
        // Arrange: Get initial state
        final notifier = container.read(unifiedNotificationSettingsNotifierProvider.notifier);

        // Act: Update prayer settings
        await notifier.updatePrayerSettings(globallyEnabled: true);

        // Assert: Should update state
        final state = await container.read(unifiedNotificationSettingsNotifierProvider.future);
        expect(state, isNotNull);
        expect(state.prayer.globallyEnabled, isTrue);
      });
    });

    group('Real-World Usage Scenarios', () {
      test('should handle rapid setting changes', () async {
        // Arrange: Get initial state
        final notifier = container.read(notificationSettingsProvider.notifier);

        // Act: Make rapid changes
        await notifier.togglePrayerNotification(PrayerType.fajr);
        await notifier.updateGlobalSound(NotificationSound.defaultSound);

        // Assert: Should handle all changes correctly
        final state = await container.read(notificationSettingsProvider.future);
        expect(state.globalSound, NotificationSound.defaultSound);
      });

      test('should handle concurrent access from multiple listeners', () async {
        // Arrange: Get initial state
        final notifier = container.read(notificationSettingsProvider.notifier);

        final List<AsyncValue<NotificationSettingsState>> capturedStates = [];

        // Act: Listen to changes while making updates
        container.listen<AsyncValue<NotificationSettingsState>>(notificationSettingsProvider, (previous, next) {
          capturedStates.add(next);
        });

        // Make multiple updates
        await notifier.togglePrayerNotification(PrayerType.fajr);
        await notifier.togglePrayerNotification(PrayerType.dhuhr);
        await notifier.togglePrayerNotification(PrayerType.asr);

        // Assert: Should capture state changes
        expect(capturedStates.length, greaterThanOrEqualTo(0));
      });

      test('should maintain performance under load', () async {
        // Arrange: Get initial state
        final notifier = container.read(notificationSettingsProvider.notifier);

        // Act: Perform many operations
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 10; i++) {
          await notifier.togglePrayerNotification(PrayerType.values[i % PrayerType.values.length]);
        }

        stopwatch.stop();

        // Assert: Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5 seconds max

        final state = await container.read(notificationSettingsProvider.future);
        expect(state, isA<NotificationSettingsState>());
      });
    });

    group('Error Handling and Recovery', () {
      test('should handle invalid state transitions gracefully', () async {
        // Arrange: Get initial state
        final notifier = container.read(notificationSettingsProvider.notifier);

        // Act: Try operations
        expect(() => notifier.togglePrayerNotification(PrayerType.fajr), returnsNormally);

        // Assert: Should maintain valid state
        final state = await container.read(notificationSettingsProvider.future);
        expect(state, isA<NotificationSettingsState>());
      });
    });
  });
}
