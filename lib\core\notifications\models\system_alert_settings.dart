import 'package:flutter/foundation.dart';

import '../services/system_alert_notification_service.dart';

/// System Alert Settings
///
/// Comprehensive system alert settings model following Context7 MCP best practices
/// for managing system alert notification preferences and behaviors.
///
/// This model provides granular control over system alerts including
/// alert type enablement, escalation settings, and notification behaviors.
@immutable
class SystemAlertSettings {
  /// Whether critical alerts are enabled
  final bool enableCriticalAlerts;

  /// Whether error alerts are enabled
  final bool enableErrorAlerts;

  /// Whether warning alerts are enabled
  final bool enableWarningAlerts;

  /// Whether security alerts are enabled
  final bool enableSecurityAlerts;

  /// Whether performance alerts are enabled
  final bool enablePerformanceAlerts;

  /// Whether info alerts are enabled
  final bool enableInfoAlerts;

  /// Whether to enable alert escalation
  final bool enableEscalation;

  /// Escalation delay for critical alerts
  final Duration criticalEscalationDelay;

  /// Escalation delay for error alerts
  final Duration errorEscalationDelay;

  /// Escalation delay for warning alerts
  final Duration warningEscalationDelay;

  /// Escalation delay for security alerts
  final Duration securityEscalationDelay;

  /// Escalation delay for performance alerts
  final Duration performanceEscalationDelay;

  /// Whether to auto-dismiss alerts on service disposal
  final bool autoDismissOnDispose;

  /// Whether to enable alert sounds
  final bool enableSounds;

  /// Whether to enable alert vibration
  final bool enableVibration;

  /// Whether to enable full-screen alerts for critical notifications
  final bool enableFullScreenAlerts;

  /// Maximum number of active alerts
  final int maxActiveAlerts;

  /// Whether to group similar alerts
  final bool groupSimilarAlerts;

  /// Alert grouping time window
  final Duration alertGroupingWindow;

  /// Whether to enable alert history
  final bool enableAlertHistory;

  /// Maximum alert history size
  final int maxAlertHistorySize;

  /// Whether to enable alert analytics
  final bool enableAlertAnalytics;

  /// Creates system alert settings with the specified configuration
  const SystemAlertSettings({
    this.enableCriticalAlerts = true,
    this.enableErrorAlerts = true,
    this.enableWarningAlerts = true,
    this.enableSecurityAlerts = true,
    this.enablePerformanceAlerts = true,
    this.enableInfoAlerts = true,
    this.enableEscalation = true,
    this.criticalEscalationDelay = const Duration(minutes: 5),
    this.errorEscalationDelay = const Duration(minutes: 10),
    this.warningEscalationDelay = const Duration(minutes: 15),
    this.securityEscalationDelay = const Duration(minutes: 3),
    this.performanceEscalationDelay = const Duration(minutes: 20),
    this.autoDismissOnDispose = false,
    this.enableSounds = true,
    this.enableVibration = true,
    this.enableFullScreenAlerts = true,
    this.maxActiveAlerts = 10,
    this.groupSimilarAlerts = true,
    this.alertGroupingWindow = const Duration(minutes: 5),
    this.enableAlertHistory = true,
    this.maxAlertHistorySize = 100,
    this.enableAlertAnalytics = true,
  });

  /// Create default system alert settings
  factory SystemAlertSettings.defaultSettings() {
    return const SystemAlertSettings(
      enableCriticalAlerts: true,
      enableErrorAlerts: true,
      enableWarningAlerts: true,
      enableSecurityAlerts: true,
      enablePerformanceAlerts: true,
      enableInfoAlerts: true,
      enableEscalation: true,
      criticalEscalationDelay: Duration(minutes: 5),
      errorEscalationDelay: Duration(minutes: 10),
      warningEscalationDelay: Duration(minutes: 15),
      securityEscalationDelay: Duration(minutes: 3),
      performanceEscalationDelay: Duration(minutes: 20),
      autoDismissOnDispose: false,
      enableSounds: true,
      enableVibration: true,
      enableFullScreenAlerts: true,
      maxActiveAlerts: 10,
      groupSimilarAlerts: true,
      alertGroupingWindow: Duration(minutes: 5),
      enableAlertHistory: true,
      maxAlertHistorySize: 100,
      enableAlertAnalytics: true,
    );
  }

  /// Create settings for minimal alerts (only critical and security)
  factory SystemAlertSettings.minimal() {
    return const SystemAlertSettings(
      enableCriticalAlerts: true,
      enableErrorAlerts: false,
      enableWarningAlerts: false,
      enableSecurityAlerts: true,
      enablePerformanceAlerts: false,
      enableInfoAlerts: false,
      enableEscalation: false,
      criticalEscalationDelay: Duration(minutes: 10),
      errorEscalationDelay: Duration(minutes: 30),
      warningEscalationDelay: Duration(minutes: 60),
      securityEscalationDelay: Duration(minutes: 5),
      performanceEscalationDelay: Duration(minutes: 60),
      autoDismissOnDispose: true,
      enableSounds: false,
      enableVibration: false,
      enableFullScreenAlerts: false,
      maxActiveAlerts: 3,
      groupSimilarAlerts: true,
      alertGroupingWindow: Duration(minutes: 10),
      enableAlertHistory: false,
      maxAlertHistorySize: 20,
      enableAlertAnalytics: false,
    );
  }

  /// Create settings for maximum alerting (all types enabled with fast escalation)
  factory SystemAlertSettings.maximum() {
    return const SystemAlertSettings(
      enableCriticalAlerts: true,
      enableErrorAlerts: true,
      enableWarningAlerts: true,
      enableSecurityAlerts: true,
      enablePerformanceAlerts: true,
      enableInfoAlerts: true,
      enableEscalation: true,
      criticalEscalationDelay: Duration(minutes: 2),
      errorEscalationDelay: Duration(minutes: 5),
      warningEscalationDelay: Duration(minutes: 10),
      securityEscalationDelay: Duration(minutes: 1),
      performanceEscalationDelay: Duration(minutes: 15),
      autoDismissOnDispose: false,
      enableSounds: true,
      enableVibration: true,
      enableFullScreenAlerts: true,
      maxActiveAlerts: 20,
      groupSimilarAlerts: false,
      alertGroupingWindow: Duration(minutes: 2),
      enableAlertHistory: true,
      maxAlertHistorySize: 500,
      enableAlertAnalytics: true,
    );
  }

  /// Create a copy with updated properties
  SystemAlertSettings copyWith({
    bool? enableCriticalAlerts,
    bool? enableErrorAlerts,
    bool? enableWarningAlerts,
    bool? enableSecurityAlerts,
    bool? enablePerformanceAlerts,
    bool? enableInfoAlerts,
    bool? enableEscalation,
    Duration? criticalEscalationDelay,
    Duration? errorEscalationDelay,
    Duration? warningEscalationDelay,
    Duration? securityEscalationDelay,
    Duration? performanceEscalationDelay,
    bool? autoDismissOnDispose,
    bool? enableSounds,
    bool? enableVibration,
    bool? enableFullScreenAlerts,
    int? maxActiveAlerts,
    bool? groupSimilarAlerts,
    Duration? alertGroupingWindow,
    bool? enableAlertHistory,
    int? maxAlertHistorySize,
    bool? enableAlertAnalytics,
  }) {
    return SystemAlertSettings(
      enableCriticalAlerts: enableCriticalAlerts ?? this.enableCriticalAlerts,
      enableErrorAlerts: enableErrorAlerts ?? this.enableErrorAlerts,
      enableWarningAlerts: enableWarningAlerts ?? this.enableWarningAlerts,
      enableSecurityAlerts: enableSecurityAlerts ?? this.enableSecurityAlerts,
      enablePerformanceAlerts: enablePerformanceAlerts ?? this.enablePerformanceAlerts,
      enableInfoAlerts: enableInfoAlerts ?? this.enableInfoAlerts,
      enableEscalation: enableEscalation ?? this.enableEscalation,
      criticalEscalationDelay: criticalEscalationDelay ?? this.criticalEscalationDelay,
      errorEscalationDelay: errorEscalationDelay ?? this.errorEscalationDelay,
      warningEscalationDelay: warningEscalationDelay ?? this.warningEscalationDelay,
      securityEscalationDelay: securityEscalationDelay ?? this.securityEscalationDelay,
      performanceEscalationDelay: performanceEscalationDelay ?? this.performanceEscalationDelay,
      autoDismissOnDispose: autoDismissOnDispose ?? this.autoDismissOnDispose,
      enableSounds: enableSounds ?? this.enableSounds,
      enableVibration: enableVibration ?? this.enableVibration,
      enableFullScreenAlerts: enableFullScreenAlerts ?? this.enableFullScreenAlerts,
      maxActiveAlerts: maxActiveAlerts ?? this.maxActiveAlerts,
      groupSimilarAlerts: groupSimilarAlerts ?? this.groupSimilarAlerts,
      alertGroupingWindow: alertGroupingWindow ?? this.alertGroupingWindow,
      enableAlertHistory: enableAlertHistory ?? this.enableAlertHistory,
      maxAlertHistorySize: maxAlertHistorySize ?? this.maxAlertHistorySize,
      enableAlertAnalytics: enableAlertAnalytics ?? this.enableAlertAnalytics,
    );
  }

  /// Check if a specific alert type is enabled
  bool isAlertTypeEnabled(SystemAlertType type) {
    switch (type) {
      case SystemAlertType.critical:
        return enableCriticalAlerts;
      case SystemAlertType.error:
        return enableErrorAlerts;
      case SystemAlertType.warning:
        return enableWarningAlerts;
      case SystemAlertType.security:
        return enableSecurityAlerts;
      case SystemAlertType.performance:
        return enablePerformanceAlerts;
      case SystemAlertType.info:
        return enableInfoAlerts;
    }
  }

  /// Get escalation delay for a specific alert type
  Duration getEscalationDelay(SystemAlertType type) {
    switch (type) {
      case SystemAlertType.critical:
        return criticalEscalationDelay;
      case SystemAlertType.error:
        return errorEscalationDelay;
      case SystemAlertType.warning:
        return warningEscalationDelay;
      case SystemAlertType.security:
        return securityEscalationDelay;
      case SystemAlertType.performance:
        return performanceEscalationDelay;
      case SystemAlertType.info:
        return const Duration(minutes: 30); // Default for info alerts
    }
  }

  /// Check if any alert type is enabled
  bool get hasAnyAlertEnabled {
    return enableCriticalAlerts ||
        enableErrorAlerts ||
        enableWarningAlerts ||
        enableSecurityAlerts ||
        enablePerformanceAlerts ||
        enableInfoAlerts;
  }

  /// Get enabled alert types
  List<SystemAlertType> get enabledAlertTypes {
    final types = <SystemAlertType>[];

    if (enableCriticalAlerts) types.add(SystemAlertType.critical);
    if (enableErrorAlerts) types.add(SystemAlertType.error);
    if (enableWarningAlerts) types.add(SystemAlertType.warning);
    if (enableSecurityAlerts) types.add(SystemAlertType.security);
    if (enablePerformanceAlerts) types.add(SystemAlertType.performance);
    if (enableInfoAlerts) types.add(SystemAlertType.info);

    return types;
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'enableCriticalAlerts': enableCriticalAlerts,
      'enableErrorAlerts': enableErrorAlerts,
      'enableWarningAlerts': enableWarningAlerts,
      'enableSecurityAlerts': enableSecurityAlerts,
      'enablePerformanceAlerts': enablePerformanceAlerts,
      'enableInfoAlerts': enableInfoAlerts,
      'enableEscalation': enableEscalation,
      'criticalEscalationDelayMs': criticalEscalationDelay.inMilliseconds,
      'errorEscalationDelayMs': errorEscalationDelay.inMilliseconds,
      'warningEscalationDelayMs': warningEscalationDelay.inMilliseconds,
      'securityEscalationDelayMs': securityEscalationDelay.inMilliseconds,
      'performanceEscalationDelayMs': performanceEscalationDelay.inMilliseconds,
      'autoDismissOnDispose': autoDismissOnDispose,
      'enableSounds': enableSounds,
      'enableVibration': enableVibration,
      'enableFullScreenAlerts': enableFullScreenAlerts,
      'maxActiveAlerts': maxActiveAlerts,
      'groupSimilarAlerts': groupSimilarAlerts,
      'alertGroupingWindowMs': alertGroupingWindow.inMilliseconds,
      'enableAlertHistory': enableAlertHistory,
      'maxAlertHistorySize': maxAlertHistorySize,
      'enableAlertAnalytics': enableAlertAnalytics,
    };
  }

  /// Validate settings consistency and correctness
  ///
  /// Context7 MCP: Comprehensive validation following defensive programming principles
  bool isValid() {
    try {
      // Validate escalation delays
      if (criticalEscalationDelay.inSeconds < 1 || criticalEscalationDelay.inSeconds > 3600) {
        return false; // Invalid delay (1 second to 1 hour)
      }

      if (errorEscalationDelay.inSeconds < 1 || errorEscalationDelay.inSeconds > 3600) {
        return false; // Invalid delay (1 second to 1 hour)
      }

      if (warningEscalationDelay.inSeconds < 1 || warningEscalationDelay.inSeconds > 3600) {
        return false; // Invalid delay (1 second to 1 hour)
      }

      if (securityEscalationDelay.inSeconds < 1 || securityEscalationDelay.inSeconds > 3600) {
        return false; // Invalid delay (1 second to 1 hour)
      }

      if (performanceEscalationDelay.inSeconds < 1 || performanceEscalationDelay.inSeconds > 3600) {
        return false; // Invalid delay (1 second to 1 hour)
      }

      // Validate max active alerts
      if (maxActiveAlerts < 1 || maxActiveAlerts > 50) {
        return false; // Invalid count (1-50)
      }

      // Validate alert grouping window
      if (alertGroupingWindow.inSeconds < 1 || alertGroupingWindow.inSeconds > 3600) {
        return false; // Invalid window (1 second to 1 hour)
      }

      // Validate max alert history size
      if (maxAlertHistorySize < 10 || maxAlertHistorySize > 10000) {
        return false; // Invalid size (10-10000)
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Create default settings
  static SystemAlertSettings defaultSettings() {
    return const SystemAlertSettings();
  }

  /// Create from JSON representation
  factory SystemAlertSettings.fromJson(Map<String, dynamic> json) {
    return SystemAlertSettings(
      enableCriticalAlerts: json['enableCriticalAlerts'] as bool? ?? true,
      enableErrorAlerts: json['enableErrorAlerts'] as bool? ?? true,
      enableWarningAlerts: json['enableWarningAlerts'] as bool? ?? true,
      enableSecurityAlerts: json['enableSecurityAlerts'] as bool? ?? true,
      enablePerformanceAlerts: json['enablePerformanceAlerts'] as bool? ?? true,
      enableInfoAlerts: json['enableInfoAlerts'] as bool? ?? true,
      enableEscalation: json['enableEscalation'] as bool? ?? true,
      criticalEscalationDelay: Duration(
        milliseconds: json['criticalEscalationDelayMs'] as int? ?? 300000, // 5 minutes
      ),
      errorEscalationDelay: Duration(
        milliseconds: json['errorEscalationDelayMs'] as int? ?? 600000, // 10 minutes
      ),
      warningEscalationDelay: Duration(
        milliseconds: json['warningEscalationDelayMs'] as int? ?? 900000, // 15 minutes
      ),
      securityEscalationDelay: Duration(
        milliseconds: json['securityEscalationDelayMs'] as int? ?? 180000, // 3 minutes
      ),
      performanceEscalationDelay: Duration(
        milliseconds: json['performanceEscalationDelayMs'] as int? ?? 1200000, // 20 minutes
      ),
      autoDismissOnDispose: json['autoDismissOnDispose'] as bool? ?? false,
      enableSounds: json['enableSounds'] as bool? ?? true,
      enableVibration: json['enableVibration'] as bool? ?? true,
      enableFullScreenAlerts: json['enableFullScreenAlerts'] as bool? ?? true,
      maxActiveAlerts: json['maxActiveAlerts'] as int? ?? 10,
      groupSimilarAlerts: json['groupSimilarAlerts'] as bool? ?? true,
      alertGroupingWindow: Duration(
        milliseconds: json['alertGroupingWindowMs'] as int? ?? 300000, // 5 minutes
      ),
      enableAlertHistory: json['enableAlertHistory'] as bool? ?? true,
      maxAlertHistorySize: json['maxAlertHistorySize'] as int? ?? 100,
      enableAlertAnalytics: json['enableAlertAnalytics'] as bool? ?? true,
    );
  }

  @override
  String toString() {
    return 'SystemAlertSettings(critical: $enableCriticalAlerts, error: $enableErrorAlerts, warning: $enableWarningAlerts, security: $enableSecurityAlerts, performance: $enablePerformanceAlerts, info: $enableInfoAlerts)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SystemAlertSettings &&
        other.enableCriticalAlerts == enableCriticalAlerts &&
        other.enableErrorAlerts == enableErrorAlerts &&
        other.enableWarningAlerts == enableWarningAlerts &&
        other.enableSecurityAlerts == enableSecurityAlerts &&
        other.enablePerformanceAlerts == enablePerformanceAlerts &&
        other.enableInfoAlerts == enableInfoAlerts &&
        other.enableEscalation == enableEscalation &&
        other.criticalEscalationDelay == criticalEscalationDelay &&
        other.errorEscalationDelay == errorEscalationDelay &&
        other.warningEscalationDelay == warningEscalationDelay &&
        other.securityEscalationDelay == securityEscalationDelay &&
        other.performanceEscalationDelay == performanceEscalationDelay &&
        other.autoDismissOnDispose == autoDismissOnDispose &&
        other.enableSounds == enableSounds &&
        other.enableVibration == enableVibration &&
        other.enableFullScreenAlerts == enableFullScreenAlerts &&
        other.maxActiveAlerts == maxActiveAlerts &&
        other.groupSimilarAlerts == groupSimilarAlerts &&
        other.alertGroupingWindow == alertGroupingWindow &&
        other.enableAlertHistory == enableAlertHistory &&
        other.maxAlertHistorySize == maxAlertHistorySize &&
        other.enableAlertAnalytics == enableAlertAnalytics;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      enableCriticalAlerts,
      enableErrorAlerts,
      enableWarningAlerts,
      enableSecurityAlerts,
      enablePerformanceAlerts,
      enableInfoAlerts,
      enableEscalation,
      criticalEscalationDelay,
      errorEscalationDelay,
      warningEscalationDelay,
      securityEscalationDelay,
      performanceEscalationDelay,
      autoDismissOnDispose,
      enableSounds,
      enableVibration,
      enableFullScreenAlerts,
      maxActiveAlerts,
      groupSimilarAlerts,
      alertGroupingWindow,
      enableAlertHistory,
      maxAlertHistorySize,
      enableAlertAnalytics,
    ]);
  }
}
