import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../lib/core/notifications/providers/unified_notification_provider.dart';
import '../../../../lib/core/notifications/models/notification_settings.dart';
import '../../../../lib/core/storage/storage_service.dart';
import '../../../../lib/core/logging/app_logger.dart';
import '../../../../lib/features/notifications/presentation/pages/notification_settings_page.dart';

/// Context7 MCP: Permission Flow User-Friendliness Tests
///
/// This test suite validates the user-friendliness of permission request flows
/// following Context7 MCP best practices for optimal user experience.
///
/// **Test Coverage:**
/// - Permission request flow usability
/// - User guidance and education
/// - Error handling and recovery
/// - Permission rationale presentation
/// - Settings integration and navigation
/// - Cross-platform consistency
///
/// **Context7 MCP Compliance:**
/// - Comprehensive permission flow testing
/// - User experience optimization
/// - Error boundary validation
/// - Accessibility compliance
/// - Performance optimization
void main() {
  group('Context7 MCP: Permission Flow User-Friendliness Tests', () {
    late ProviderContainer container;

    /// Context7 MCP: Test setup with comprehensive permission flow testing environment
    setUp(() async {
      // Initialize Flutter test binding for UI tests
      TestWidgetsFlutterBinding.ensureInitialized();

      // Create container with minimal overrides
      container = ProviderContainer();

      AppLogger.info('🧪 Test setup completed for permission flow user-friendliness tests');
    });

    /// Context7 MCP: Proper resource cleanup
    tearDown(() async {
      container.dispose();
      AppLogger.info('🧹 Test cleanup completed');
    });

    group('Permission Request Flow Usability', () {
      testWidgets('should provide clear permission request rationale', (tester) async {
        // Context7 MCP: Test permission rationale presentation
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Look for permission rationale UI elements
        final rationaleIndicators = [
          find.textContaining('permission'),
          find.textContaining('Permission'),
          find.textContaining('allow'),
          find.textContaining('Allow'),
          find.textContaining('notification'),
          find.textContaining('Notification'),
        ];

        final hasRationaleText = rationaleIndicators.any((finder) => finder.evaluate().isNotEmpty);
        expect(hasRationaleText, isTrue, reason: 'UI should provide clear rationale for permission requests');

        // Verify rationale is presented before permission request
        final explanatoryText = find.byType(Text);
        expect(explanatoryText, findsWidgets, reason: 'Permission rationale should include explanatory text');

        AppLogger.info('✅ Permission rationale presentation test passed');
      });

      testWidgets('should handle permission request gracefully', (tester) async {
        // Context7 MCP: Test graceful permission request handling
        var permissionRequested = false;

        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => false);
        when(mockPermissionService.requestNotificationPermission()).thenAnswer((_) async {
          permissionRequested = true;
          return true;
        });

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Find and tap a toggle that requires permissions
        final toggleFinder = find.byType(Switch);
        if (toggleFinder.evaluate().isNotEmpty) {
          await act.tap(spot<Switch>().first());
          await tester.pumpAndSettle();

          // Verify permission was requested
          expect(
            permissionRequested,
            isTrue,
            reason: 'Permission should be requested when user attempts to enable notifications',
          );
        }

        AppLogger.info('✅ Permission request handling test passed');
      });

      testWidgets('should provide immediate feedback after permission grant', (tester) async {
        // Context7 MCP: Test immediate feedback after permission grant
        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => false);
        when(mockPermissionService.requestNotificationPermission()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Simulate permission grant by updating mock
        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => true);

        // Trigger permission request
        final toggleFinder = find.byType(Switch);
        if (toggleFinder.evaluate().isNotEmpty) {
          await act.tap(spot<Switch>().first());
          await tester.pumpAndSettle();

          // Verify UI provides immediate feedback
          final feedbackIndicators = [
            find.byType(SnackBar),
            find.textContaining('granted'),
            find.textContaining('Granted'),
            find.textContaining('enabled'),
            find.textContaining('Enabled'),
            find.byIcon(Icons.check),
            find.byIcon(Icons.check_circle),
          ];

          final hasFeedback = feedbackIndicators.any((finder) => finder.evaluate().isNotEmpty);

          // Either explicit feedback or toggle state change indicates success
          final toggleWidget = tester.widget<Switch>(toggleFinder.first);
          final hasVisualFeedback = hasFeedback || toggleWidget.value == true;

          expect(hasVisualFeedback, isTrue, reason: 'UI should provide immediate feedback after permission is granted');
        }

        AppLogger.info('✅ Immediate feedback after permission grant test passed');
      });
    });

    group('User Guidance and Education', () {
      testWidgets('should educate users about notification benefits', (tester) async {
        // Context7 MCP: Test user education about notification benefits
        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => false);

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Look for educational content about notification benefits
        final educationalContent = [
          find.textContaining('prayer'),
          find.textContaining('Prayer'),
          find.textContaining('reminder'),
          find.textContaining('Reminder'),
          find.textContaining('alert'),
          find.textContaining('Alert'),
          find.textContaining('important'),
          find.textContaining('Important'),
        ];

        final hasEducationalContent = educationalContent.any((finder) => finder.evaluate().isNotEmpty);
        expect(
          hasEducationalContent,
          isTrue,
          reason: 'UI should educate users about the benefits of enabling notifications',
        );

        AppLogger.info('✅ User education about notification benefits test passed');
      });

      testWidgets('should provide contextual help for permission decisions', (tester) async {
        // Context7 MCP: Test contextual help for permission decisions
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Look for help or info icons/buttons
        final helpIndicators = [
          find.byIcon(Icons.help),
          find.byIcon(Icons.help_outline),
          find.byIcon(Icons.info),
          find.byIcon(Icons.info_outline),
          find.textContaining('help'),
          find.textContaining('Help'),
          find.textContaining('info'),
          find.textContaining('Info'),
        ];

        final hasHelpIndicators = helpIndicators.any((finder) => finder.evaluate().isNotEmpty);

        // Also check for descriptive text that serves as contextual help
        final descriptiveText = find.byType(Text);
        final hasDescriptiveContent = descriptiveText.evaluate().length > 1; // More than just titles

        expect(
          hasHelpIndicators || hasDescriptiveContent,
          isTrue,
          reason: 'UI should provide contextual help for permission decisions',
        );

        AppLogger.info('✅ Contextual help for permission decisions test passed');
      });
    });

    group('Error Handling and Recovery', () {
      testWidgets('should handle permission denial gracefully', (tester) async {
        // Context7 MCP: Test graceful handling of permission denial
        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => false);
        when(mockPermissionService.requestNotificationPermission()).thenAnswer((_) async => false);

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Attempt to enable notifications (which will be denied)
        final toggleFinder = find.byType(Switch);
        if (toggleFinder.evaluate().isNotEmpty) {
          await act.tap(spot<Switch>().first());
          await tester.pumpAndSettle();

          // Verify graceful handling of denial
          final denialHandlingIndicators = [
            find.byType(SnackBar),
            find.textContaining('denied'),
            find.textContaining('Denied'),
            find.textContaining('permission'),
            find.textContaining('Permission'),
            find.byIcon(Icons.error),
            find.byIcon(Icons.warning),
          ];

          final hasGracefulHandling = denialHandlingIndicators.any((finder) => finder.evaluate().isNotEmpty);

          // Verify toggle doesn't remain in inconsistent state
          final toggleWidget = tester.widget<Switch>(toggleFinder.first);
          final toggleStateConsistent = toggleWidget.value == false;

          expect(
            hasGracefulHandling || toggleStateConsistent,
            isTrue,
            reason: 'UI should handle permission denial gracefully',
          );
        }

        AppLogger.info('✅ Permission denial handling test passed');
      });

      testWidgets('should provide recovery options for permanently denied permissions', (tester) async {
        // Context7 MCP: Test recovery options for permanently denied permissions
        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => false);
        when(mockPermissionService.requestNotificationPermission()).thenAnswer((_) async => false);
        when(mockPermissionService.isPermanentlyDenied()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Attempt to enable notifications (permanently denied)
        final toggleFinder = find.byType(Switch);
        if (toggleFinder.evaluate().isNotEmpty) {
          await act.tap(spot<Switch>().first());
          await tester.pumpAndSettle();

          // Look for recovery options
          final recoveryOptions = [
            find.textContaining('settings'),
            find.textContaining('Settings'),
            find.textContaining('manually'),
            find.textContaining('Manually'),
            find.textContaining('open'),
            find.textContaining('Open'),
            find.byType(ElevatedButton),
            find.byType(TextButton),
          ];

          final hasRecoveryOptions = recoveryOptions.any((finder) => finder.evaluate().isNotEmpty);
          expect(
            hasRecoveryOptions,
            isTrue,
            reason: 'UI should provide recovery options for permanently denied permissions',
          );
        }

        AppLogger.info('✅ Recovery options for permanently denied permissions test passed');
      });
    });

    group('Permission Rationale Presentation', () {
      testWidgets('should show rationale before first permission request', (tester) async {
        // Context7 MCP: Test rationale presentation before first request
        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => false);
        when(mockPermissionService.shouldShowRequestRationale()).thenAnswer((_) async => false);

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Look for pre-request rationale
        final rationaleElements = [
          find.textContaining('why'),
          find.textContaining('Why'),
          find.textContaining('need'),
          find.textContaining('Need'),
          find.textContaining('important'),
          find.textContaining('Important'),
          find.textContaining('benefit'),
          find.textContaining('Benefit'),
        ];

        final hasRationale = rationaleElements.any((finder) => finder.evaluate().isNotEmpty);
        expect(hasRationale, isTrue, reason: 'UI should show rationale before first permission request');

        AppLogger.info('✅ Rationale before first permission request test passed');
      });

      testWidgets('should provide detailed rationale after denial', (tester) async {
        // Context7 MCP: Test detailed rationale after permission denial
        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => false);
        when(mockPermissionService.shouldShowRequestRationale()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Look for detailed rationale after denial
        final detailedRationale = [
          find.textContaining('reconsider'),
          find.textContaining('Reconsider'),
          find.textContaining('please'),
          find.textContaining('Please'),
          find.textContaining('allow'),
          find.textContaining('Allow'),
          find.textContaining('enable'),
          find.textContaining('Enable'),
        ];

        final hasDetailedRationale = detailedRationale.any((finder) => finder.evaluate().isNotEmpty);

        // Also check for longer explanatory text
        final textWidgets = find.byType(Text);
        final hasLongerExplanation = textWidgets.evaluate().any((element) {
          final widget = element.widget as Text;
          final text = widget.data ?? '';
          return text.length > 50; // Longer explanatory text
        });

        expect(
          hasDetailedRationale || hasLongerExplanation,
          isTrue,
          reason: 'UI should provide detailed rationale after permission denial',
        );

        AppLogger.info('✅ Detailed rationale after denial test passed');
      });
    });

    group('Settings Integration and Navigation', () {
      testWidgets('should provide easy navigation to app settings', (tester) async {
        // Context7 MCP: Test navigation to app settings
        when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => false);
        when(mockPermissionService.isPermanentlyDenied()).thenAnswer((_) async => true);

        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Look for settings navigation options
        final settingsNavigation = [
          find.textContaining('settings'),
          find.textContaining('Settings'),
          find.textContaining('open settings'),
          find.textContaining('Open Settings'),
          find.byIcon(Icons.settings),
          find.byIcon(Icons.open_in_new),
        ];

        final hasSettingsNavigation = settingsNavigation.any((finder) => finder.evaluate().isNotEmpty);
        expect(hasSettingsNavigation, isTrue, reason: 'UI should provide easy navigation to app settings');

        AppLogger.info('✅ Navigation to app settings test passed');
      });

      testWidgets('should maintain state after returning from settings', (tester) async {
        // Context7 MCP: Test state maintenance after returning from settings
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Capture initial state
        final initialToggles = find.byType(Switch);
        final initialToggleStates = <bool>[];

        for (final toggleElement in initialToggles.evaluate()) {
          final toggleWidget = toggleElement.widget as Switch;
          initialToggleStates.add(toggleWidget.value);
        }

        // Simulate app lifecycle change (going to settings and back)
        tester.binding.defaultBinaryMessenger.setMockMethodCallHandler(const MethodChannel('flutter/lifecycle'), (
          call,
        ) async {
          if (call.method == 'AppLifecycleState.resumed') {
            // Simulate returning from settings with permission granted
            when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => true);
          }
          return null;
        });

        // Trigger app resume
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/lifecycle',
          const StandardMethodCodec().encodeMethodCall(const MethodCall('AppLifecycleState.resumed')),
          (data) {},
        );

        await tester.pumpAndSettle();

        // Verify state is maintained or appropriately updated
        final finalToggles = find.byType(Switch);
        expect(
          finalToggles.evaluate().length,
          equals(initialToggles.evaluate().length),
          reason: 'UI should maintain consistent structure after returning from settings',
        );

        AppLogger.info('✅ State maintenance after returning from settings test passed');
      });
    });

    group('Cross-Platform Consistency', () {
      testWidgets('should provide consistent permission flow across platforms', (tester) async {
        // Context7 MCP: Test cross-platform consistency
        await tester.pumpWidget(
          UncontrolledProviderScope(
            container: container,
            child: MaterialApp(home: NotificationSettingsPage()),
          ),
        );

        await tester.pumpAndSettle();

        // Verify consistent UI elements across platforms
        final consistentElements = [find.byType(Switch), find.byType(Text), find.byType(AppBar)];

        for (final element in consistentElements) {
          expect(element, findsWidgets, reason: 'UI should have consistent elements across platforms');
        }

        // Verify permission flow follows platform conventions
        final platformConventions = [
          find.byType(MaterialApp), // Material Design for Android
          // Could add Cupertino checks for iOS
        ];

        final followsConventions = platformConventions.any((finder) => finder.evaluate().isNotEmpty);
        expect(followsConventions, isTrue, reason: 'Permission flow should follow platform design conventions');

        AppLogger.info('✅ Cross-platform consistency test passed');
      });
    });
  });

  /// Context7 MCP: Configure default mock behaviors
  void _configureDefaultMocks() {
    when(mockPermissionService.hasNotificationPermission()).thenAnswer((_) async => true);
    when(mockPermissionService.requestNotificationPermission()).thenAnswer((_) async => true);
    when(mockPermissionService.shouldShowRequestRationale()).thenAnswer((_) async => false);
    when(mockPermissionService.isPermanentlyDenied()).thenAnswer((_) async => false);
    when(mockPermissionService.openAppSettings()).thenAnswer((_) async => true);

    when(
      mockStorageService.getObject<NotificationSettings>('notification_settings', any),
    ).thenAnswer((_) async => NotificationSettings.defaultSettings());

    when(
      mockStorageService.getObject<Map<String, bool>>('notification_permissions', any),
    ).thenAnswer((_) async => <String, bool>{});

    when(mockStorageService.getInt('notification_settings_migration_version')).thenAnswer((_) async => 1);

    when(mockStorageService.setObject(any, any)).thenAnswer((_) async {});
    when(mockStorageService.setInt(any, any)).thenAnswer((_) async {});
  }
}
