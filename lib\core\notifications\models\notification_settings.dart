import 'package:flutter/foundation.dart';

import 'notification_channel.dart';

/// Notification Settings
///
/// Comprehensive notification settings model following Context7 MCP best practices
/// for managing user preferences and notification behavior across the application.
///
/// This model provides granular control over notification settings including
/// global settings, per-channel settings, quiet hours, and advanced features.
@immutable
class NotificationSettings {
  /// Whether notifications are globally enabled
  final bool globallyEnabled;

  /// Per-channel notification settings
  final Map<NotificationChannelKey, NotificationChannelSettings> channelSettings;

  /// Global quiet hours start time (24-hour format)
  final int? globalQuietHoursStart;

  /// Global quiet hours end time (24-hour format)
  final int? globalQuietHoursEnd;

  /// Whether to respect system Do Not Disturb mode
  final bool respectDoNotDisturb;

  /// Whether to show notifications when app is in foreground
  final bool showInForeground;

  /// Whether to group notifications by channel
  final bool groupNotifications;

  /// Maximum number of notifications to show
  final int maxNotifications;

  /// Whether to clear notifications on app dispose
  final bool clearOnDispose;

  /// Whether to use system notification sound
  final bool useSystemSound;

  /// Whether to use system vibration pattern
  final bool useSystemVibration;

  /// Custom notification sound file path
  final String? customSoundPath;

  /// Custom vibration pattern
  final List<int>? customVibrationPattern;

  /// Whether to show notification previews on lock screen
  final bool showPreviewsOnLockScreen;

  /// Whether to show notification badges
  final bool showBadges;

  /// Badge count mode
  final BadgeCountMode badgeCountMode;

  /// Notification history retention days
  final int historyRetentionDays;

  /// Whether to enable notification analytics
  final bool enableAnalytics;

  /// Advanced notification features
  final AdvancedNotificationFeatures advancedFeatures;

  /// Creates notification settings with the specified configuration
  const NotificationSettings({
    this.globallyEnabled = true,
    this.channelSettings = const {},
    this.globalQuietHoursStart,
    this.globalQuietHoursEnd,
    this.respectDoNotDisturb = true,
    this.showInForeground = true,
    this.groupNotifications = true,
    this.maxNotifications = 50,
    this.clearOnDispose = false,
    this.useSystemSound = true,
    this.useSystemVibration = true,
    this.customSoundPath,
    this.customVibrationPattern,
    this.showPreviewsOnLockScreen = true,
    this.showBadges = true,
    this.badgeCountMode = BadgeCountMode.unread,
    this.historyRetentionDays = 30,
    this.enableAnalytics = true,
    this.advancedFeatures = const AdvancedNotificationFeatures(),
  });

  /// Create default notification settings
  factory NotificationSettings.defaultSettings() {
    return const NotificationSettings(
      globallyEnabled: true,
      channelSettings: {},
      respectDoNotDisturb: true,
      showInForeground: true,
      groupNotifications: true,
      maxNotifications: 50,
      clearOnDispose: false,
      useSystemSound: true,
      useSystemVibration: true,
      showPreviewsOnLockScreen: true,
      showBadges: true,
      badgeCountMode: BadgeCountMode.unread,
      historyRetentionDays: 30,
      enableAnalytics: true,
      advancedFeatures: AdvancedNotificationFeatures(),
    );
  }

  /// Create a copy with updated properties
  NotificationSettings copyWith({
    bool? globallyEnabled,
    Map<NotificationChannelKey, NotificationChannelSettings>? channelSettings,
    int? globalQuietHoursStart,
    int? globalQuietHoursEnd,
    bool? respectDoNotDisturb,
    bool? showInForeground,
    bool? groupNotifications,
    int? maxNotifications,
    bool? clearOnDispose,
    bool? useSystemSound,
    bool? useSystemVibration,
    String? customSoundPath,
    List<int>? customVibrationPattern,
    bool? showPreviewsOnLockScreen,
    bool? showBadges,
    BadgeCountMode? badgeCountMode,
    int? historyRetentionDays,
    bool? enableAnalytics,
    AdvancedNotificationFeatures? advancedFeatures,
  }) {
    return NotificationSettings(
      globallyEnabled: globallyEnabled ?? this.globallyEnabled,
      channelSettings: channelSettings ?? this.channelSettings,
      globalQuietHoursStart: globalQuietHoursStart ?? this.globalQuietHoursStart,
      globalQuietHoursEnd: globalQuietHoursEnd ?? this.globalQuietHoursEnd,
      respectDoNotDisturb: respectDoNotDisturb ?? this.respectDoNotDisturb,
      showInForeground: showInForeground ?? this.showInForeground,
      groupNotifications: groupNotifications ?? this.groupNotifications,
      maxNotifications: maxNotifications ?? this.maxNotifications,
      clearOnDispose: clearOnDispose ?? this.clearOnDispose,
      useSystemSound: useSystemSound ?? this.useSystemSound,
      useSystemVibration: useSystemVibration ?? this.useSystemVibration,
      customSoundPath: customSoundPath ?? this.customSoundPath,
      customVibrationPattern: customVibrationPattern ?? this.customVibrationPattern,
      showPreviewsOnLockScreen: showPreviewsOnLockScreen ?? this.showPreviewsOnLockScreen,
      showBadges: showBadges ?? this.showBadges,
      badgeCountMode: badgeCountMode ?? this.badgeCountMode,
      historyRetentionDays: historyRetentionDays ?? this.historyRetentionDays,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
      advancedFeatures: advancedFeatures ?? this.advancedFeatures,
    );
  }

  /// Check if a specific channel is enabled
  bool isChannelEnabled(NotificationChannelKey channelKey) {
    if (!globallyEnabled) return false;

    final channelSetting = channelSettings[channelKey];
    if (channelSetting == null) return true; // Default to enabled

    return channelSetting.enabled;
  }

  /// Check if currently in global quiet hours
  bool get isInGlobalQuietHours {
    if (globalQuietHoursStart == null || globalQuietHoursEnd == null) {
      return false;
    }

    final now = DateTime.now();
    final currentHour = now.hour;

    if (globalQuietHoursStart! <= globalQuietHoursEnd!) {
      // Same day quiet hours
      return currentHour >= globalQuietHoursStart! && currentHour < globalQuietHoursEnd!;
    } else {
      // Overnight quiet hours
      return currentHour >= globalQuietHoursStart! || currentHour < globalQuietHoursEnd!;
    }
  }

  /// Check if a channel is in quiet hours
  bool isChannelInQuietHours(NotificationChannelKey channelKey) {
    // Check global quiet hours first
    if (isInGlobalQuietHours) return true;

    // Check channel-specific quiet hours
    final channelSetting = channelSettings[channelKey];
    return channelSetting?.isInQuietHours() ?? false;
  }

  /// Get effective sound setting for a channel
  bool getEffectiveSoundSetting(NotificationChannelKey channelKey) {
    if (!globallyEnabled) return false;

    final channelSetting = channelSettings[channelKey];
    return channelSetting?.customSound ?? useSystemSound;
  }

  /// Get effective vibration setting for a channel
  bool getEffectiveVibrationSetting(NotificationChannelKey channelKey) {
    if (!globallyEnabled) return false;

    final channelSetting = channelSettings[channelKey];
    return channelSetting?.customVibration ?? useSystemVibration;
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'globallyEnabled': globallyEnabled,
      'channelSettings': channelSettings.map((key, value) => MapEntry(key.name, value.toJson())),
      'globalQuietHoursStart': globalQuietHoursStart,
      'globalQuietHoursEnd': globalQuietHoursEnd,
      'respectDoNotDisturb': respectDoNotDisturb,
      'showInForeground': showInForeground,
      'groupNotifications': groupNotifications,
      'maxNotifications': maxNotifications,
      'clearOnDispose': clearOnDispose,
      'useSystemSound': useSystemSound,
      'useSystemVibration': useSystemVibration,
      'customSoundPath': customSoundPath,
      'customVibrationPattern': customVibrationPattern,
      'showPreviewsOnLockScreen': showPreviewsOnLockScreen,
      'showBadges': showBadges,
      'badgeCountMode': badgeCountMode.name,
      'historyRetentionDays': historyRetentionDays,
      'enableAnalytics': enableAnalytics,
      'advancedFeatures': advancedFeatures.toJson(),
    };
  }

  /// Validate settings consistency and correctness
  ///
  /// Context7 MCP: Comprehensive validation following defensive programming principles
  bool isValid() {
    try {
      // Validate quiet hours
      if (globalQuietHoursStart != null && (globalQuietHoursStart! < 0 || globalQuietHoursStart! > 23)) {
        return false; // Invalid hour (0-23)
      }

      if (globalQuietHoursEnd != null && (globalQuietHoursEnd! < 0 || globalQuietHoursEnd! > 23)) {
        return false; // Invalid hour (0-23)
      }

      // Validate max notifications
      if (maxNotifications < 1 || maxNotifications > 1000) {
        return false; // Invalid count (1-1000)
      }

      // Validate history retention
      if (historyRetentionDays < 1 || historyRetentionDays > 365) {
        return false; // Invalid days (1-365)
      }

      // Validate channel settings
      for (final channelSetting in channelSettings.values) {
        if (!_isValidChannelSetting(channelSetting)) {
          return false;
        }
      }

      // Validate advanced features
      if (!_isValidAdvancedFeatures(advancedFeatures)) {
        return false;
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Validate individual channel setting
  bool _isValidChannelSetting(NotificationChannelSettings setting) {
    try {
      // Validate quiet hours
      if (setting.quietHoursStart != null && (setting.quietHoursStart! < 0 || setting.quietHoursStart! > 23)) {
        return false; // Invalid hour (0-23)
      }

      if (setting.quietHoursEnd != null && (setting.quietHoursEnd! < 0 || setting.quietHoursEnd! > 23)) {
        return false; // Invalid hour (0-23)
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Validate advanced features
  bool _isValidAdvancedFeatures(AdvancedNotificationFeatures features) {
    try {
      // Validate max bundle size
      if (features.maxBundleSize < 1 || features.maxBundleSize > 100) {
        return false;
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Create default settings
  static NotificationSettings defaultSettings() {
    return const NotificationSettings();
  }

  /// Create from JSON representation
  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    final channelSettingsMap = <NotificationChannelKey, NotificationChannelSettings>{};
    final channelSettingsJson = json['channelSettings'] as Map<String, dynamic>? ?? {};

    for (final entry in channelSettingsJson.entries) {
      final channelKey = NotificationChannelKey.values.firstWhere(
        (e) => e.name == entry.key,
        orElse: () => NotificationChannelKey.general,
      );
      channelSettingsMap[channelKey] = NotificationChannelSettings.fromJson(entry.value as Map<String, dynamic>);
    }

    return NotificationSettings(
      globallyEnabled: json['globallyEnabled'] as bool? ?? true,
      channelSettings: channelSettingsMap,
      globalQuietHoursStart: json['globalQuietHoursStart'] as int?,
      globalQuietHoursEnd: json['globalQuietHoursEnd'] as int?,
      respectDoNotDisturb: json['respectDoNotDisturb'] as bool? ?? true,
      showInForeground: json['showInForeground'] as bool? ?? true,
      groupNotifications: json['groupNotifications'] as bool? ?? true,
      maxNotifications: json['maxNotifications'] as int? ?? 50,
      clearOnDispose: json['clearOnDispose'] as bool? ?? false,
      useSystemSound: json['useSystemSound'] as bool? ?? true,
      useSystemVibration: json['useSystemVibration'] as bool? ?? true,
      customSoundPath: json['customSoundPath'] as String?,
      customVibrationPattern: (json['customVibrationPattern'] as List<dynamic>?)?.map((e) => e as int).toList(),
      showPreviewsOnLockScreen: json['showPreviewsOnLockScreen'] as bool? ?? true,
      showBadges: json['showBadges'] as bool? ?? true,
      badgeCountMode: BadgeCountMode.values.firstWhere(
        (e) => e.name == json['badgeCountMode'],
        orElse: () => BadgeCountMode.unread,
      ),
      historyRetentionDays: json['historyRetentionDays'] as int? ?? 30,
      enableAnalytics: json['enableAnalytics'] as bool? ?? true,
      advancedFeatures: json['advancedFeatures'] != null
          ? AdvancedNotificationFeatures.fromJson(json['advancedFeatures'] as Map<String, dynamic>)
          : const AdvancedNotificationFeatures(),
    );
  }

  @override
  String toString() {
    return 'NotificationSettings(globallyEnabled: $globallyEnabled, channels: ${channelSettings.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationSettings &&
        other.globallyEnabled == globallyEnabled &&
        mapEquals(other.channelSettings, channelSettings) &&
        other.globalQuietHoursStart == globalQuietHoursStart &&
        other.globalQuietHoursEnd == globalQuietHoursEnd &&
        other.respectDoNotDisturb == respectDoNotDisturb &&
        other.showInForeground == showInForeground &&
        other.groupNotifications == groupNotifications &&
        other.maxNotifications == maxNotifications &&
        other.clearOnDispose == clearOnDispose &&
        other.useSystemSound == useSystemSound &&
        other.useSystemVibration == useSystemVibration &&
        other.customSoundPath == customSoundPath &&
        listEquals(other.customVibrationPattern, customVibrationPattern) &&
        other.showPreviewsOnLockScreen == showPreviewsOnLockScreen &&
        other.showBadges == showBadges &&
        other.badgeCountMode == badgeCountMode &&
        other.historyRetentionDays == historyRetentionDays &&
        other.enableAnalytics == enableAnalytics &&
        other.advancedFeatures == advancedFeatures;
  }

  @override
  int get hashCode {
    return Object.hash(
      globallyEnabled,
      channelSettings,
      globalQuietHoursStart,
      globalQuietHoursEnd,
      respectDoNotDisturb,
      showInForeground,
      groupNotifications,
      maxNotifications,
      clearOnDispose,
      useSystemSound,
      useSystemVibration,
      customSoundPath,
      customVibrationPattern,
      showPreviewsOnLockScreen,
      showBadges,
      badgeCountMode,
      historyRetentionDays,
      enableAnalytics,
      advancedFeatures,
    );
  }
}

/// Badge Count Mode
///
/// Defines how notification badges should be counted.
enum BadgeCountMode {
  /// Count only unread notifications
  unread,

  /// Count all notifications
  all,

  /// Count only high priority notifications
  highPriority,

  /// Disable badge counting
  disabled,
}

/// Advanced Notification Features
///
/// Configuration for advanced notification features and behaviors.
@immutable
class AdvancedNotificationFeatures {
  /// Whether to enable smart notification grouping
  final bool smartGrouping;

  /// Whether to enable notification scheduling optimization
  final bool scheduleOptimization;

  /// Whether to enable battery optimization awareness
  final bool batteryOptimization;

  /// Whether to enable adaptive notification timing
  final bool adaptiveTiming;

  /// Whether to enable notification priority learning
  final bool priorityLearning;

  /// Whether to enable location-based notifications
  final bool locationBased;

  /// Whether to enable notification bundling
  final bool bundling;

  /// Maximum bundle size
  final int maxBundleSize;

  /// Creates advanced notification features with the specified configuration
  const AdvancedNotificationFeatures({
    this.smartGrouping = true,
    this.scheduleOptimization = true,
    this.batteryOptimization = true,
    this.adaptiveTiming = false,
    this.priorityLearning = false,
    this.locationBased = false,
    this.bundling = true,
    this.maxBundleSize = 5,
  });

  /// Create a copy with updated properties
  AdvancedNotificationFeatures copyWith({
    bool? smartGrouping,
    bool? scheduleOptimization,
    bool? batteryOptimization,
    bool? adaptiveTiming,
    bool? priorityLearning,
    bool? locationBased,
    bool? bundling,
    int? maxBundleSize,
  }) {
    return AdvancedNotificationFeatures(
      smartGrouping: smartGrouping ?? this.smartGrouping,
      scheduleOptimization: scheduleOptimization ?? this.scheduleOptimization,
      batteryOptimization: batteryOptimization ?? this.batteryOptimization,
      adaptiveTiming: adaptiveTiming ?? this.adaptiveTiming,
      priorityLearning: priorityLearning ?? this.priorityLearning,
      locationBased: locationBased ?? this.locationBased,
      bundling: bundling ?? this.bundling,
      maxBundleSize: maxBundleSize ?? this.maxBundleSize,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'smartGrouping': smartGrouping,
      'scheduleOptimization': scheduleOptimization,
      'batteryOptimization': batteryOptimization,
      'adaptiveTiming': adaptiveTiming,
      'priorityLearning': priorityLearning,
      'locationBased': locationBased,
      'bundling': bundling,
      'maxBundleSize': maxBundleSize,
    };
  }

  /// Create from JSON representation
  factory AdvancedNotificationFeatures.fromJson(Map<String, dynamic> json) {
    return AdvancedNotificationFeatures(
      smartGrouping: json['smartGrouping'] as bool? ?? true,
      scheduleOptimization: json['scheduleOptimization'] as bool? ?? true,
      batteryOptimization: json['batteryOptimization'] as bool? ?? true,
      adaptiveTiming: json['adaptiveTiming'] as bool? ?? false,
      priorityLearning: json['priorityLearning'] as bool? ?? false,
      locationBased: json['locationBased'] as bool? ?? false,
      bundling: json['bundling'] as bool? ?? true,
      maxBundleSize: json['maxBundleSize'] as int? ?? 5,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdvancedNotificationFeatures &&
        other.smartGrouping == smartGrouping &&
        other.scheduleOptimization == scheduleOptimization &&
        other.batteryOptimization == batteryOptimization &&
        other.adaptiveTiming == adaptiveTiming &&
        other.priorityLearning == priorityLearning &&
        other.locationBased == locationBased &&
        other.bundling == bundling &&
        other.maxBundleSize == maxBundleSize;
  }

  @override
  int get hashCode {
    return Object.hash(
      smartGrouping,
      scheduleOptimization,
      batteryOptimization,
      adaptiveTiming,
      priorityLearning,
      locationBased,
      bundling,
      maxBundleSize,
    );
  }
}
